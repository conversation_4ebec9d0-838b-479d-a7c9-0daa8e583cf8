def create_action_mapping():
    mapping = {}
    
    # SalesStack action mapping
    mapping['SalesStack'] = {
        'owner': 'sales_poc',
        'states': {
            'clarification_needed': {
                'action_required': 'Respond to the clarification query',
                'owner': 'sales_poc',
                'tat_hours': 12
            },
            'pricing_quotation_generated': {
                'action_required': 'Share quotation with the customer and fill feedback',
                'owner': 'sales_poc',
                'tat_hours': 48
            },
            'quote_expired': {
                'action_required': 'Share quotation with the customer and fill feedback',
                'owner': 'sales_poc',
                'tat_hours': 48
            },
            'sample_shipped': {
                'action_required': 'View Tracking Details',
                'owner': 'sales_poc',
                'tat_hours': 12
            },
            'sample_delivered': {
                'action_required': 'Follow up with the customer and fill sample feedback',
                'owner': 'sales_poc',
                'tat_hours': 24
            },
        
        }
    }
    
    # ProcureStack action mapping
    mapping['ProcureStack'] = {
        'owner': 'procurement_poc',
        'states': {
            'enquiry_received': {
                'action_required': 'Assign Enquiry',
                'owner': 'bu_admin',
                'tat_hours': 12
            },
            'enquiry_assigned': {
                'action_required': 'Find Suppliers',
                'owner': 'procurement_poc',
                'tat_hours': 24
            },
            'quote_prepared': {
                'action_required': 'Approve/Reject Quote',
                'owner': 'bu_admin',
                'tat_hours': 12
            },
            'quote_revision_needed': {
                'action_required': 'Revise Quote & Send for Approval',
                'owner': 'procurement_poc',
                'tat_hours': 12

            },
            'quote_approved': {
                'action_required': 'Prepare Pricing Quotation for customer',
                'owner': 'procurement_poc',
                'tat_hours': 12

            },
            'quote_redo': {
                'action_required': 'Prepare Quotation basis feedback',
                'owner': 'procurement_poc',
                'tat_hours': 12

            },
            'sample_request_received': {
                'action_required': 'Initiate Sample request to Supplier',
                'owner': 'procurement_poc',
                'tat_hours': 12
            },
            'sample_request_raised': {
                'action_required': 'Initiate Sample Movement Preparation',
                'owner': 'logistics_poc',
                'tat_hours': 12
            }
            ,
            'sample_delivered': {
                'action_required': 'Initimation (no action required)',
                'owner': 'procurement_poc',
                'tat_hours': 12,
            },
            'po_raised': {
                'action_required': 'Create Order on LogiStack',
                'owner': 'procurement_poc',
                'tat_hours': 12
            }
        }
    }
    
    # LogiStack action mapping
    mapping['LogiStack'] = {
        'owner': 'logistics_poc',
        'states': {
            'Sample production complete': {
                'action_required': 'Sample production complete is completed',
                'owner': 'logistics_poc',
                'tat_hours': 12
            },
            'Testing completed': {
                'action_required': 'Mstack Documentation & Approval for movement',
                'owner': 'procurement_poc',
                'tat_hours': 12
            },
            'Approval for movement': {
                'action_required': 'Sample Dispatch to customer',
                'owner': 'logistics_poc',
                'tat_hours': 12
                
            },
            'Margin Approval':{
                'action_required': 'Margin Approval / Reject on the order',
                'owner': 'finance_poc,business_head',
                'tat_hours': 12
            },
            'Advance to Supplier':{
                'action_required': 'Advance payment to be made to the supplier',
                'owner': 'finance_poc',
                'tat_hours': 24

            },
            'supplier po':{
                'action_required': 'Booking Confirmation required',
                'owner': 'logistics_poc',
                'tat_hours': 24

            },
            'Testing Results Received':{
                'action_required': 'Approval required',
                'owner': 'procurement_poc',
                'tat_hours': 12

            },
            'Documentation':{
                'action_required': 'Approval required',
                'owner': 'logistics_poc',
                'tat_hours': 12

            },
            'Packaging/Labelling':{
                'action_required': 'Complete Packaging & Labelling',
                'owner': 'logistics_poc',
                'tat_hours': 12

            },
            'QC Approval':{
                'action_required': 'Approval required',
                'owner': 'procurement_poc',
                'tat_hours': 24

            },
            'Delivery Confirmation':{
                'action_required': 'Initimation (no action required)',
                'owner': 'procurement_poc',
                'tat_hours': 12

            }

        }
    }
    return mapping
