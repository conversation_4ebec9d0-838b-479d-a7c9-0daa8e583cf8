import traceback
import logging
import requests
from ses_hook import SESHook
from config.db_config import DatabaseConfig
from airflow.models import Variable




def send_exception_slack_alert(exception_message, context):
    if not context:
        return
    slack_webhook_url = Variable.get("slack_webhook_url")  # Set this in Airflow Variables
    env_type = Variable.get("mstack_deployment_env")
    slack_message = f"""
    🚨 *{env_type.upper()} AIRFLOW Exception FAILURE ALERT* 🚨
    
    📋 *Task Details*
    • *DAG:* `{context.get('dag').dag_id}`
    • *Task:* `{context.get('task_instance').task_id}`
    • *Execution Time:* `{context.get('execution_date')}`
    
    🔗 *Log URL:* {context.get('task_instance').log_url}
    
    ❌ *Error Details*
    • *Exception Message:*
    ```{exception_message}```
    
    • *Full Traceback:*
    ```{str(traceback.format_exc())}```
    """
    slack_response = requests.post(
            slack_webhook_url,
            json={"text": slack_message}
        )
    if slack_response.status_code != 200:
        logging.warning(f"Request to <PERSON>lack returned error {slack_response.status_code}, response: {slack_response.text}")
    else:
        logging.info("Failure alert sent to Slack successfully.")
