import boto3
import logging
from config.db_config import DatabaseConfig

class SESHook:
    def __init__(self, region_name=None, access_key=None, secret_key=None, sender=None):
        """
        Initialize SES client with credentials from DatabaseConfig or explicit values
        
        :param region_name: AWS region name (overrides config if provided)
        :param access_key: AWS access key ID (overrides config if provided)
        :param secret_key: AWS secret access key (overrides config if provided)
        :param sender: Email sender address (overrides config if provided)
        """
        # Get AWS SES configuration from DatabaseConfig
        aws_config = DatabaseConfig.get_aws_ses_params()
        
        # Use provided values or fall back to config values
        self.region_name = region_name or aws_config['region']
        self.access_key = access_key or aws_config['access_key_id']
        self.secret_key = secret_key or aws_config['secret_access_key']
        self.sender = sender or aws_config['sender_email']
        
    def send_email(self, sender=None, recipient=None, subject=None, html_body=None):
        """
        Send email using direct boto3 SES client
        
        :param sender: Email sender address (overrides default if provided)
        :param recipient: List of recipient email addresses
        :param subject: Email subject
        :param html_body: HTML body content
        :return: Response from SES API
        """
        try:
            # Use provided sender or fall back to default
            email_sender = sender or self.sender
            
            # Create SES client with explicit credentials
            if self.access_key and self.secret_key:
                client = boto3.client(
                    "ses",
                    region_name=self.region_name,
                    aws_access_key_id=self.access_key,
                    aws_secret_access_key=self.secret_key,
                )
            else:
                # If no explicit credentials, use default credentials chain
                client = boto3.client("ses", region_name=self.region_name)
            
            # Log the attempt
            logging.info(f"Sending email via SES in region {self.region_name}")
            
            # Send the email
            response = client.send_email(
                Source=email_sender,
                Destination={"ToAddresses": recipient},
                Message={
                    "Subject": {"Data": subject},
                    "Body": {
                        "Html": {
                            "Data": html_body,
                            "Charset": "UTF-8"
                        }
                    },
                },
            )
            
            logging.info(f"Email sent! Message ID: {response.get('MessageId', 'unknown')}")
            return response
            
        except Exception as e:
            logging.error(f"Error sending email via SES: {str(e)}")
            raise
