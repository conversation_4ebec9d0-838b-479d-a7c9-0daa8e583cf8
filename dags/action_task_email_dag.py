import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
import os
from datetime import datetime, timedelta
from jinja2 import Environment, FileSystemLoader
from db.postgres_operations import PostgresOperations
from db.mongo_operations import MongoOperations
from config.db_config import DatabaseConfig
from config.action_mapping import create_action_mapping
from ses_hook import SESHook
from zoneinfo import ZoneInfo
from airflow import DAG
from airflow.operators.python import PythonOperator
from functools import partial
from airflow.models import Variable
from typing import List, Dict, Any, Optional, Tuple


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')

# Constants
DAG_ID = 'action_email_dag'
DEFAULT_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] #,  #['<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>']
DEFAULT_TASK_RETRIES = 3
DEFAULT_TASK_RETRY_DELAY = timedelta(minutes=5)
DEFAULT_TASK_TIMEOUT = timedelta(hours=1)

# Get start date from Airflow variables or use default
START_DATE = Variable.get(
    "email_notification_start_date",
    default_var=datetime(2024, 5, 15, tzinfo=ZoneInfo("Asia/Kolkata"))
)

# Task list for MongoDB pipeline
TASK_LIST = [
    'Sample production complete',
    'Approval before dispatching to Customer',
    'Testing completed',
    'Approval for movement',
    'Margin Approval',
    'Advance to Supplier',
    'supplier po',
    'Testing Results Received',
    'Documentation',
    'Packaging/Labelling',
    'QC Approval',
    'Delivery Confirmation'
]

# MongoDB aggregation pipeline for activity tracking
MONGO_PIPELINE = [
    # 1. Match documents with specific conditions
    {
        '$match': {
            'deleted': False,
            'status': 'COMPLETED',
            'createdAt': {'$gt': START_DATE},
            'name': {'$in': TASK_LIST}
        }
    },
    # 2. Convert _id to string for email log lookup
    {
        '$addFields': {
            'id_string': {'$toString': '$_id'}
        }
    },
    # 3. Lookup email notification logs
    {
        '$lookup': {
            'from': 'email_notification_history',
            'localField': 'id_string',
            'foreignField': 'document_ref_id',
            'as': 'email_logs'
        }
    },
    # 4. Filter out documents that already have email logs
    {
        '$match': {
            'email_logs': {'$size': 0}
        }
    },
    # 5. Join with orderBook collection using orderId
    {
        '$lookup': {
            'from': 'orderBook',
            'localField': 'secondaryId',
            'foreignField': 'purchaseOrderNumber',
            'as': 'order_data'
        }
    },
    # 6. Unwind the order_data array
    {
        '$unwind': {
            'path': '$order_data',
            'preserveNullAndEmptyArrays': True
        }
    },
    # 7. Safely convert assignedTo to ObjectId with fallback
    {
        '$addFields': {
            'assignedToObjectId': {
                '$convert': {
                    'input': '$assignedTo',
                    'to': 'objectId',
                    'onError': None,
                    'onNull': None
                }
            }
        }
    },
    # 8. Lookup employee data using assignedToObjectId
    {
        '$lookup': {
            'from': 'employee',
            'localField': 'assignedToObjectId',
            'foreignField': '_id',
            'as': 'employee_data'
        }
    },
    # 9. Unwind employee_data array
    {
        '$unwind': {
            'path': '$employee_data',
            'preserveNullAndEmptyArrays': True
        }
    },
    # 10. Final projection of required fields
    {
        '$project': {
            'activity_id': '$_id',
            'secondaryId': 1,
            'name': 1,
            'taskCompletedOn': 1,
            'orderBookId': '$order_data._id',
            'employee_email': '$employee_data.email',
            'tz_info' : '$employee_data.timezone',
            'customerName': 1
        }
    }
]

# DAG default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': DEFAULT_TASK_RETRIES,
    'retry_delay': DEFAULT_TASK_RETRY_DELAY,
    'execution_timeout': DEFAULT_TASK_TIMEOUT
}

# Task definitions
TASK_DEFINITIONS: List[Tuple[str, str]] = [
    ('sales_stack_clarification_read', 'sales_stack_clarification_read'),
    ('sales_stack_pricing_quote_read', 'sales_stack_pricing_quote_read'),
    ('sales_stack_pricing_quote_expiry_read', 'sales_stack_pricing_quote_expiry_read'),
    ('sales_stack_sample_tracking', 'sales_stack_sample_tracking'),
    ('sales_stack_sample_delivered', 'sales_stack_sample_delivered'),
    ('procure_stack_enquiry_received', 'procure_stack_enquiry_received'),
    ('procure_stack_enquiry_assigned', 'procure_stack_enquiry_assigned'),
    ('procure_stack_quote_prepared', 'procure_stack_quote_prepared'),
    ('procure_stack_quote_revision_approved_redo_po_raised', 'procure_stack_quote_revision_approved_redo_po_raised'),
    ('procure_stack_sample_requested_delivered', 'procure_stack_sample_requested_delivered'),
    ('logi_stack_mail', 'logi_stack_mail')
]

class EmailNotificationManager:
    """Manages email notifications for different stacks (Sales, Procure, Logi)."""
    
    def __init__(self) -> None:
        """Initialize the EmailNotificationManager with required connections and configurations."""
        logger.info("Initializing EmailNotificationManager")
        self.sales_db: Optional[PostgresOperations] = None
        self.procure_db: Optional[PostgresOperations] = None
        self.logi_db: Optional[MongoOperations] = None
       
        self.action_mapping = create_action_mapping()
        logger.info("Action mapping created successfully")
        
        self.template_env = Environment(
            loader=FileSystemLoader(TEMPLATE_DIR),
            autoescape=True
        )
        self.initialize_connections()
        self.ses_hook = SESHook()
        self.aws_config = DatabaseConfig.get_aws_ses_params()
        self.sender_email = self.aws_config['sender_email']
        self.all_tasks = {'sample_request_raised'}  # Changed to set for better performance
        self.insert_query = """INSERT INTO public.email_notification_history 
                             (email_type, document_ref_id, state)
                             VALUES (:email_type, :document_ref_id, :state)
                             ON CONFLICT (email_type, document_ref_id, state) DO NOTHING"""
        logger.info("EmailNotificationManager initialized successfully")

    def initialize_connections(self) -> None:
        """Initialize database connections for all stacks."""
        logger.info("Initializing database connections")
        try:
            sales_params = DatabaseConfig.get_sales_stack_postgres_params()
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            logi_params = DatabaseConfig.get_logi_stack_mongo_params() 
            
            self.sales_db = PostgresOperations(sales_params)
            self.procure_db = PostgresOperations(procure_params)
            self.logi_db = MongoOperations(logi_params[0], logi_params[1])
            
            logger.info("Database connections initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database connections: {str(e)}")
            raise

    def close_connections(self) -> None:
        """Safely close all database connections."""
        logger.info("Closing database connections")
        try:
            if self.sales_db:
                self.sales_db.close()
                logger.info("Sales DB connection closed")

            if self.procure_db:
                self.procure_db.close()
                logger.info("Procure DB connection closed")

            if self.logi_db:
                self.logi_db.close()
                logger.info("Logi DB connection closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {str(e)}")

    def render_email_template(self, template_name: str, **kwargs) -> str:
        """
        Render the email template with provided context.
        
        Args:
            template_name: Name of the template to render
            **kwargs: Context variables for template rendering
            
        Returns:
            str: Rendered HTML content
            
        Raises:
            Exception: If template rendering fails
        """
        logger.info(f"Rendering email template: {template_name}")
        try:
            template = self.template_env.get_template(template_name)
            return template.render(**kwargs)
        except Exception as e:
            logger.error(f"Error rendering email template {template_name}: {str(e)}")
            raise
    
    def get_action_link(self, stack: str, enquiry_id: str, state: str = '') -> Optional[str]:
        """
        Get the action link for the task based on stack and state.
        
        Args:
            stack: Stack name (SalesStack, ProcureStack, or LogiStack)
            enquiry_id: ID of the enquiry
            state: Current state of the task
            
        Returns:
            Optional[str]: Action link URL or None if not found
        """
        logger.info(f"Getting action link for stack: {stack}, enquiry_id: {enquiry_id}, state: {state}")
        try:
            if stack == 'ProcureStack':
                if state in ('enquiry_received', 'quote_prepared'):
                    base_url = Variable.get("procure_admin_url", default_var=None)
                else:
                    base_url = Variable.get("procure_dashboard_url", default_var=None)
            elif stack == 'SalesStack':
                base_url = Variable.get("salesstack_url", default_var=None)
            elif stack == 'LogiStack':
                base_url = Variable.get("logistack_url", default_var=None)
            else:
                logger.warning(f"No action link found for stack: {stack}")
                return None
                
            return base_url.format(enquiry_id=enquiry_id) if base_url else None
            
        except Exception as e:
            logger.error(f"Error getting action link: {str(e)}")
            return None
    
    def send_email(self, recipient: list[str], html_content: str) -> None:
        """
        Send email using SES.
        
        Args:
            recipient: Email address of the recipient
            html_content: HTML content of the email
            
        Raises:
            Exception: If email sending fails
        """
        subject = "Action Required for Your Tasks"
        logger.info(f"Sending email to recipient: {recipient}")
        try:
            response = self.ses_hook.send_email(
                sender=self.sender_email,
                recipient=recipient,
                subject=subject,
                html_body=html_content
            )
            message_id = response.get('MessageId', 'unknown').replace('\n', '').replace('\r', '')
            logger.info(f"Email sent successfully! Message ID: {message_id}")
        except Exception as e:
            logger.error(f"Error sending email to {recipient}: {str(e)}")
            raise

    def sales_stack_clarification_read(self):
        doc_state = 'clarification_needed'
        select_query = """SELECT c.id, e.enquiry_id, e.sales_team_member, e.customer_full_name
                          , e.enquiry_id as deep_link_document_id, c.created_at as last_task_completed_at, ur.timezone
                            FROM public.enquiry_clarifications c 
                          inner join public.enquiries e on c.enquiry_id = e.id
                          left join public.email_notification_history enh on c.id::text = enh.document_ref_id and enh.state = :doc_state and enh.email_type = 'ActionRequired'
                          left join auth.users au on e.sales_team_member = au.email 
                            left join user_roles ur on ur.user_id = au.id
                          WHERE enh.id is null and c.status ='pending'"""

        self.sales_stack_mail(select_query, doc_state)

    def sales_stack_pricing_quote_read(self):
        doc_state = 'pricing_quotation_generated'
        select_query  =  """SELECT c.id, e.enquiry_id, e.sales_team_member, e.customer_full_name
                            , e.enquiry_id as deep_link_document_id, c.generated_at as last_task_completed_at, ur.timezone
                            FROM public.quote_generation_details c  
                            inner join public.enquiries e on c.enquiry_id = e.id
                            left join public.email_notification_history enh on c.id::text = enh.document_ref_id and enh.state = :doc_state and enh.email_type = 'ActionRequired'
                            left join auth.users au on e.sales_team_member = au.email 
                            left join user_roles ur on ur.user_id = au.id
                            WHERE enh.id is null;"""

        self.sales_stack_mail(select_query, doc_state)

    def sales_stack_pricing_quote_expiry_read(self):
        doc_state = 'quote_expired'
        select_query  =  """SELECT c.id, e.enquiry_id, e.sales_team_member, e.customer_full_name, qgo.expiry_date
                                 ,e.enquiry_id as deep_link_document_id, qgo.expiry_date as last_task_completed_at, ur.timezone
                            FROM public.quote_generation_details c inner join public.quote_generation_options qgo on c.id = qgo.quote_generation_id
                            inner join public.enquiries e on c.enquiry_id = e.id
                            left join public.email_notification_history enh on c.id::text = enh.document_ref_id and enh.state = :doc_state and enh.email_type = 'ActionRequired'
                            left join auth.users au on e.sales_team_member = au.email 
                            left join user_roles ur on ur.user_id = au.id
                            WHERE enh.id is null and qgo.expiry_date>= now() - INTERVAL '4 days';"""

        self.sales_stack_mail(select_query, doc_state)

    def sales_stack_sample_tracking(self):
        doc_state = 'sample_shipped'
        select_query  =  """SELECT sr.id, e.enquiry_id, e.sales_team_member, e.customer_full_name,
                            e.enquiry_id as deep_link_document_id, sr.last_status_change as last_task_completed_at, ur.timezone
                            FROM public.sample_requests sr
                            inner join public.enquiries e on sr.enquiry_id = e.id
                            left join public.email_notification_history enh on sr.id::text = enh.document_ref_id and enh.state = :doc_state and enh.email_type = 'ActionRequired'
                            left join auth.users au on e.sales_team_member = au.email 
                            left join user_roles ur on ur.user_id = au.id
                            WHERE enh.id is null and tracking_url is not null;"""

        self.sales_stack_mail(select_query, doc_state)

    def sales_stack_sample_delivered(self):
        doc_state = 'sample_delivered'
        select_query  =  """SELECT esh.id, e.enquiry_id, e.sales_team_member, e.customer_full_name
                            , e.enquiry_id as deep_link_document_id, esh.created_at as last_task_completed_at, ur.timezone
                            FROM enquiries e inner join public.enquiry_status_history esh on e.id = esh.enquiry_id and esh.status = :doc_state
                            left join public.email_notification_history enh on esh.id::text = enh.document_ref_id and esh.status::text = enh.state and enh.email_type = 'ActionRequired'
                            left join auth.users au on e.sales_team_member = au.email 
                            left join user_roles ur on ur.user_id = au.id
                            where esh.status = :doc_state and enh.id is null;"""

        self.sales_stack_mail(select_query, doc_state)

    def procure_stack_enquiry_received(self):
        doc_state = 'enquiry_received'
        select_query =  """SELECT distinct
                                e.id,
                                e.enquiry_id,
                                string_agg(DISTINCT r.email, ',') as recipient_email,
                                e.customer_full_name,
                                esh.new_state as state,
                                e.enquiry_id as deep_link_document_id,
                                esh.created_at as last_task_completed_at,
                                r.timezone
                            FROM profiles r
                            INNER JOIN business_units b ON r.business_unit_id = b.id
                            INNER JOIN public.enquiries e ON b.name = e.category
                            INNER JOIN public.status_changes esh ON e.id = esh.enquiry_id AND esh.new_state =:doc_state
                            LEFT JOIN public.email_notification_history enh ON esh.enquiry_id::text = enh.document_ref_id AND esh.new_state = enh.state and enh.email_type = 'ActionRequired'
                            WHERE enh.id IS NULL AND role_code = 'bu_head'
                            GROUP BY e.id, e.enquiry_id, e.customer_full_name, esh.new_state, esh.created_at, r.timezone;"""


        self.procure_stack_mail(select_query, doc_state)

    def procure_stack_enquiry_assigned(self):
        doc_state = 'enquiry_assigned'
        select_query = """SELECT distinct
                                e.id,
                                e.enquiry_id,
                                string_agg(DISTINCT r.email, ',') as recipient_email,
                                e.customer_full_name,
                                esh.new_state as state,
                                e.enquiry_id as deep_link_document_id,
                                esh.created_at as last_task_completed_at,
                                r.timezone
                            FROM profiles r
                            INNER JOIN business_units b ON r.business_unit_id = b.id
                            INNER JOIN public.enquiries e ON b.name = e.category
                            INNER JOIN public.status_changes esh ON e.id = esh.enquiry_id AND esh.new_state =:doc_state
                            LEFT JOIN public.email_notification_history enh ON esh.enquiry_id::text = enh.document_ref_id AND esh.new_state = enh.state and enh.email_type = 'ActionRequired'
                            WHERE enh.id IS NULL AND role_code = 'bu_head'
                            GROUP BY e.id, e.enquiry_id, e.customer_full_name, esh.new_state, esh.created_at, r.timezone;"""


        self.procure_stack_mail(select_query, doc_state)

    def procure_stack_quote_prepared(self):
        doc_state = 'quote_prepared'
        select_query = """SELECT distinct
                                e.id,
                                e.enquiry_id,
                                string_agg(DISTINCT r.email, ',') as recipient_email,
                                e.customer_full_name,
                                esh.new_state as state,
                                e.enquiry_id as deep_link_document_id,
                                esh.created_at as last_task_completed_at,
                                r.timezone

                            FROM profiles r
                            INNER JOIN business_units b ON r.business_unit_id = b.id
                            INNER JOIN public.enquiries e ON b.name = e.category
                            INNER JOIN public.status_changes esh ON e.id = esh.enquiry_id AND esh.new_state =:doc_state
                            LEFT JOIN public.email_notification_history enh ON esh.enquiry_id::text = enh.document_ref_id AND esh.new_state = enh.state and enh.email_type = 'ActionRequired'
                            WHERE enh.id IS NULL AND role_code = 'bu_head'
                            GROUP BY e.id, e.enquiry_id, e.customer_full_name, esh.new_state, esh.created_at, r.timezone;"""

        self.procure_stack_mail(select_query, doc_state)

    def procure_stack_quote_revision_approved_redo_po_raised(self):
        doc_state = ('quote_revision_needed', 'quote_approved', 'quote_redo', 'po_raised')
        select_query = """SELECT distinct e.id, e.enquiry_id, p.email as recipient_email, e.customer_full_name, esh.new_state as state
                        , e.enquiry_id as deep_link_document_id, esh.created_at as last_task_completed_at, p.timezone
                        FROM public.enquiries e 
                        inner join public.status_changes esh on e.id = esh.enquiry_id and esh.new_state::text IN :doc_state
                        inner join profiles p on p.id:: text = e.procurement_poc_id
                        left join public.email_notification_history enh on e.id::text = enh.document_ref_id and enh.state::text IN :doc_state and enh.email_type = 'ActionRequired'
                        WHERE enh.id is null;"""

        self.procure_stack_mail(select_query, doc_state)

    def procure_stack_sample_requested_delivered(self):
        doc_state = ('sample_request_received', 'sample_delivered','sample_request_raised')
        select_query = """SELECT distinct srd.id, e.enquiry_id, p.email as recipient_email, e.customer_full_name, srd.status as state,
                            e.enquiry_id as deep_link_document_id, ssh.changed_at as last_task_completed_at, p.timezone

                            FROM public.sample_requests srd
                            inner join public.sample_status_history ssh on srd.id:: text = ssh.sample_request_id:: text and ssh.sample_status::text = srd.status
                            inner join public.enquiries e on  e.id:: text = srd.enquiry_id:: text
                            inner join profiles p on p.id:: text = e.procurement_poc_id
                            left join public.email_notification_history enh on srd.id::text = enh.document_ref_id and enh.state::text = srd.status and enh.email_type = 'ActionRequired'
                            WHERE enh.id is null and srd.status in :doc_state;"""


        self.procure_stack_mail(select_query, doc_state)


    def sales_stack_mail(self, select_query, doc_state):
        logger.info(f"Processing sales stack mail for doc_state: {doc_state}")
        try:
            data = self.sales_db.read_data(select_query, {"doc_state":doc_state})
            if data:
                logger.info(f"Found {len(data)} records for sales stack mail processing")
                for row in data:
                    logger.info(f"Processing sales stack mail for : {row}")
                    self.send_data(row['enquiry_id'], row['customer_full_name'], doc_state , row['sales_team_member']
                                ,'SalesStack', row['deep_link_document_id'], row['last_task_completed_at'], row['timezone'])
                    insert_dict = {"email_type":"ActionRequired","document_ref_id": row['id'], "state":doc_state}
                    self.sales_db.write_data(self.insert_query, insert_dict)
                    logger.info(f"Successfully processed and recorded sales stack mail for enquiry_id: {row['enquiry_id']}")
            else:
                logger.info(f"No records found for sales stack mail processing with doc_state: {doc_state}")
        except Exception as e:
            logger.error(f"Error processing sales stack mail for doc_state {doc_state}: {str(e)}")
            raise

    def procure_stack_mail(self, select_query, doc_state):
        logger.info(f"Processing procure stack mail for doc_state: {doc_state}")
        try:
            data = self.procure_db.read_data(select_query, {"doc_state":doc_state})
            if data:
                logger.info(f"Found {len(data)} records for procure stack mail processing")
                for row in data:
                    logger.info(f"Processing procure stack mail for : {row}")
                    self.send_data(row['enquiry_id'], row['customer_full_name'], row['state'] , row['recipient_email']
                                ,'ProcureStack', row['deep_link_document_id'], row['last_task_completed_at'], row['timezone'])

                    insert_dict = {"email_type":"ActionRequired","document_ref_id": row['id'], "state":row['state']}
                    self.procure_db.write_data(self.insert_query, insert_dict)
                    logger.info(f"Successfully processed and recorded procure stack mail for enquiry_id: {row['enquiry_id']}")
            else:
                logger.info(f"No records found for procure stack mail processing with doc_state: {doc_state}")
        except Exception as e:
            logger.error(f"Error processing procure stack mail for doc_state {doc_state}: {str(e)}")
            raise

    def logi_stack_mail(self) -> None:
        """
        Process and send notifications for logistics stack activities.
        
        This method:
        1. Executes the MongoDB aggregation pipeline to find activities requiring notifications
        2. Processes each activity and sends notifications
        3. Records the notification in the email history
        
        Raises:
            Exception: If processing or sending fails
        """
        logger.info("Starting logistics stack mail processing")
        try:
            # Execute the aggregation pipeline
            activities = self.logi_db.aggregate(MONGO_PIPELINE, "activity")
            
            if activities:
                logger.info(f"Found {len(activities)} activities requiring notifications")
                for activity in activities:
                    logger.info(f"Processing activity: {activity}")
                    # Check if we have the necessary data
                    if all(key in activity for key in ['activity_id', 'secondaryId', 'orderBookId']):
                        # Send notification email
                        self.send_data(
                            activity.get('secondaryId', ''),
                            activity.get('customerName', 'Customer'),
                            activity.get('name', ''),
                            activity.get('employee_email', ''),
                            'LogiStack',
                            activity.get('orderBookId', ''),
                            activity.get('taskCompletedOn', datetime.now(ZoneInfo("Asia/Kolkata"))),
                            activity.get('tz_info', 'Asia/Kolkata')
                        )
                        
                        # Record that we sent the notification in MongoDB
                        notification_doc = {
                            "email_type": "ActionRequired",
                            "document_ref_id": str(activity['activity_id']),
                            "state": activity['name'],
                            "created_at": datetime.now(ZoneInfo("Asia/Kolkata"))
                        }
                        self.logi_db.write_data("email_notification_history", notification_doc)
                        logger.info(f"Recorded email notification for activity {activity['activity_id']}")
                    else:
                        missing_keys = [key for key in ['activity_id', 'secondaryId', 'employee_email', 'orderBookId'] 
                                      if key not in activity]
                        logger.warning(f"Skipping activity due to missing data. Missing keys: {missing_keys}")
            else:
                logger.info("No activities found requiring notifications")
                
        except Exception as e:
            logger.error(f"Error processing logistics stack mail: {str(e)}")
            raise

    def send_data(
        self,
        enquiry_id: str,
        customer_name: str,
        states: str,
        email: str,
        stack: str,
        deep_link_document_id: str,
        last_task_completed_at: datetime,
        tz_info: str = 'Asia/Kolkata'
    ) -> None:
        """
        Process and send action required email.
        
        Args:
            enquiry_id: ID of the enquiry
            customer_name: Name of the customer
            states: Current state of the task
            email: Recipient email address
            stack: Stack name
            deep_link_document_id: ID for deep linking
            last_task_completed_at: Timestamp of last task completion
            
        Raises:
            Exception: If processing or sending fails
        """
        try:
            stack_states = self.action_mapping.get(stack, {}).get('states', {})
            action_required = stack_states.get(states, {}).get('action_required', '')
            
            logger.info(f"Processing action required for enquiry_id: {enquiry_id}, state: {states}, stack: {stack}")
            
            # Get TAT hours with validation
            tat_hours = stack_states.get(states, {}).get('tat_hours')
            if not tat_hours:
                logger.warning(f"No TAT hours found for state {states} in stack {stack}")
                tat_hours = 12 # Default TAT
            if not tz_info:
                tz_info = 'Asia/Kolkata'
            due_datetime = (last_task_completed_at.astimezone(ZoneInfo(tz_info)) + timedelta(hours=tat_hours)).strftime('%d-%b-%Y %H:%M %Z')
            owner_name = ' '.join(
                word.capitalize() for word in (email or '@').replace('.', ' ').split('@')[0].split(' '))

            if isinstance(email, str):
                # Check if it's a comma-separated string
                if ',' in email:
                    email = [e.strip() for e in email.split(',')]
                else:
                    email = [email]
            
            if (states in ['sample_request_raised','Testing completed','Margin Approval' ,'Advance to Supplier',
                           'Testing Results Received','Delivery Confirmation','Documentation']
                    or email is None or email == '' or not isinstance(email, str) or email.strip() == ''):
                email = DEFAULT_EMAILS

            email = DEFAULT_EMAILS

            task_list = [{
                'enquiry_id': enquiry_id,
                'customer_name': customer_name,
                'current_status': states.replace('_', ' ').title() if '_' in states else states,
                'action_required': action_required,
                'due_date': due_datetime,
                'action_link': self.get_action_link(stack, deep_link_document_id, states)
            }]

            html_content = self.render_email_template(
                'action_required_email_template.html',
                tasks=task_list,
                stack_name=stack,
                sales_team_member= owner_name,
                generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
            )
            self.send_email(email, html_content)
            logger.info(f"Action required email sent to {email} for enquiry_id: {enquiry_id}")

        except Exception as e:
            logger.error(f"Error processing action required for enquiry_id {enquiry_id}: {str(e)}")
            raise


def sync_data(sync_func: str) -> None:
    """
    Generic sync function that creates EmailNotificationManager instance and calls specified sync method.
    
    Args:
        sync_func: Name of the sync function to call
        
    Raises:
        Exception: If the sync function fails
    """
    logger.info(f"Starting sync function: {sync_func}")
    action_mails = None
    try:
        action_mails = EmailNotificationManager()
        getattr(action_mails, sync_func)()
        logger.info(f"Successfully completed sync function: {sync_func}")
    except Exception as e:
        logger.error(f"Error in sync function {sync_func}: {str(e)}")
        raise
    finally:
        if action_mails:
            action_mails.close_connections()

# DAG definition
with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    description='Send action required email notifications for Sales, Procure & Logi stacks',
    schedule='*/10 * * * *',  # Run every 10 minutes
    catchup=False,
    tags=['email', 'notification'],
    max_active_runs=1,
    dagrun_timeout=timedelta(minutes=50),  # Auto-fail after 30 minutes
) as dag:
    
    # Create tasks dynamically
    tasks: Dict[str, PythonOperator] = {}
    
    for task_name, sync_function in TASK_DEFINITIONS:
        tasks[task_name] = PythonOperator(
            task_id=f'send_{task_name}',
            python_callable=partial(sync_data, sync_function),
            retries=DEFAULT_TASK_RETRIES,
            retry_delay=DEFAULT_TASK_RETRY_DELAY,
            execution_timeout=DEFAULT_TASK_TIMEOUT,
        )
    
    # Set task dependencies
    for i in range(len(TASK_DEFINITIONS) - 1):
        tasks[TASK_DEFINITIONS[i][0]] >> tasks[TASK_DEFINITIONS[i + 1][0]]