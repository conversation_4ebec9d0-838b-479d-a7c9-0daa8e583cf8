import logging
import os
from datetime import datetime, timezone
from airflow import DAG
from db.mongo_operations import MongoOperations
from config.default import DEFAULT_DAG_ARGS
from config.db_config import DatabaseConfig
from ses_hook import SESHook  # Import the custom hook
from jinja2 import Template, FileSystemLoader, Environment

# Path to templates directory
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')

employee_delayed_tasks_pipeLine = [
            {
                "$match": {
                    'dueDate': {'$lt': datetime.now(tz=timezone.utc)}, # change to $lt for past tasks $gt for testing
                    'deleted': False,
                    'status': {'$ne': "COMPLETED"}
                }
            },
            {
                "$set": {
                    "assignedTo": {
                        "$convert": {
                            "input": "$assignedTo",
                            "to": "objectId",
                            "onError": None,   # Prevents errors if `assignedTo` is not a valid ObjectId
                            "onNull": None
                        }
                    }
                }
            },
            {
                "$lookup": {
                    "from": "employee",
                    "localField": "assignedTo",
                    "foreignField": "_id",
                    "as": "assignedToDetails"
                }
            },
            {
                "$unwind": {
                    "path": "$assignedToDetails",
                    "preserveNullAndEmptyArrays": False  # Removes docs where no matching employee exists
                }
            },
            {
                "$group": {
                    "_id": "$assignedTo",
                    "assignedTo": {"$first": {"$ifNull": ["$assignedToDetails.name", "Unknown Employee"]}},
                    "email": {"$first": {"$ifNull": ["$assignedToDetails.email", "<EMAIL>"]}},
                    "activities": {
                        "$push": {
                            "entityType": "$entityType",
                            "status": "$status",
                            "entityId": "$entityId",
                            "orderId": "$orderId",
                            "name": "$name",
                            "group": "$group",
                            "dueDate": "$dueDate"
                        }
                    }
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "assignedTo": 1,
                    "email": 1,
                    "activities": 1
                }
            }
        ]

def get_delayed_tasks():
    """Get all tasks from MongoDB that are past their due date with employee details"""
    # Initialize MongoDB connection using MongoOperations
    mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
    mongo_ops = MongoOperations(mongo_conn_string, mongo_db)
    
    try:
        logging.info("Running aggregation query for delayed tasks")
        # Use the aggregate function with our pipeline
        results = mongo_ops.aggregate(employee_delayed_tasks_pipeLine, collection_name="activity")
        
        # Log the count of results
        result_count = len(results)
        logging.info(f"Found {result_count} employees with delayed tasks")
        
        if result_count == 0:
            logging.warning("No delayed tasks found for any employees")
            
        return results
    except Exception as e:
        logging.error(f"Error retrieving delayed tasks: {str(e)}")
        # Return empty list in case of error
        return []
    finally:
        # Make sure to close the connection
        mongo_ops.close()

def generate_html_report(**context):
    """Generate HTML reports for each employee with their delayed tasks"""
    delayed_tasks_by_employee = get_delayed_tasks()

    logging.info(f"Retrieved delayed tasks for employees")

    # Create a dictionary to store HTML content for each employee
    employee_reports = {}
    
    if not delayed_tasks_by_employee:
        logging.warning("No delayed tasks found to report")
        return {}
    
    logging.info(f"Processing delayed tasks for {len(delayed_tasks_by_employee)} employees")
    
    # Set up Jinja2 environment with the template directory
    jinja_env = Environment(loader=FileSystemLoader(TEMPLATE_DIR))
    template = jinja_env.get_template('employee_email_template.html')
    
    for employee_data in delayed_tasks_by_employee:
        employee_email = employee_data.get('email')
        if not employee_email:
            logging.warning(f"No email found for employee: {employee_data.get('assignedTo', 'Unknown')}")
            continue  # Skip if no email address
        
        activities = employee_data.get('activities', [])
        if not activities:
            logging.info(f"No activities found for employee {employee_email}")
            continue
            
        logging.info(f"Processing {len(activities)} delayed tasks for {employee_email}")
            
        # Format the tasks data for this employee
        tasks_data = []
        for activity in activities:
            try:
                due_date_str = activity.get('dueDate').strftime('%Y-%m-%d %H:%M:%S UTC') if activity.get('dueDate') else 'N/A'
            except Exception as e:
                logging.error(f"Error formatting date for activity: {str(e)}")
                due_date_str = 'Error with date'
                
            tasks_data.append({
                'entityId': activity.get('entityId', 'N/A'),
                'orderId': activity.get('orderId', 'N/A'),
                'entityType': activity.get('entityType', 'N/A'),
                'dueDate': due_date_str,
                'assignedTo': employee_data.get('assignedTo', 'N/A'),
                'email': employee_email,
                'status': activity.get('status', 'N/A'),
                'group': activity.get('group', 'N/A')
            })
        
        # Sort tasks by due date
        tasks_data.sort(key=lambda x: x['dueDate'] if x['dueDate'] != 'N/A' and x['dueDate'] != 'Error with date' else '9999-12-31')
        
        # Generate personalized HTML for this employee
        try:
            html_content = template.render(
                delayed_tasks=tasks_data,
                generation_time=datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
            )
            
            # Store the HTML report for this employee
            employee_reports[employee_email] = {
                'name': employee_data.get('assignedTo', 'Employee'),
                'html': html_content
            }
            logging.info(f"Generated HTML report for {employee_email}")
        except Exception as e:
            logging.error(f"Error generating HTML for {employee_email}: {str(e)}")
    
    logging.info(f"Generated reports for {len(employee_reports)} employees")
    # Return the dictionary of reports
    return employee_reports

def send_email(**context):
    """Send personalized emails to each employee using AWS SES with direct boto3"""
    # Get the employee reports from the previous task
    employee_reports = context['task_instance'].xcom_pull(task_ids='generate_html_report')
    
    if not employee_reports:
        logging.warning("No employee reports to send")
        return {'sent': 0, 'failed': 0, 'details': {}}
    
    aws_config = DatabaseConfig.get_aws_ses_params()
    sender_email = aws_config['sender_email']

    # Create an instance of SESHook
    ses_hook = SESHook()
    # Track results
    results = {}
    sent_count = 0
    failed_count = 0
    
    # Send individual emails to each employee
    for email, report_data in employee_reports.items():
        subject = f"Your Delayed Tasks Report - Action Required"
        
        try:
            response = ses_hook.send_email(
                sender=sender_email,
                recipient=[email],
                subject=subject,
                html_body=report_data['html']
            )
            results[email] = f"Success: MessageId={response.get('MessageId', 'unknown')}"
            sent_count += 1
        except Exception as e:
            results[email] = f"Failed: {str(e)}"
            failed_count += 1
    
    # Log summary of email sending
    summary = {
        'sent': sent_count,
        'failed': failed_count,
        'details': results
    }
    logging.info(f"Email sending summary: {summary}")
    return summary

with DAG(
    'delayed_tasks_email_dag',
    default_args=DEFAULT_DAG_ARGS,
    description='DAG to send personalized emails for delayed tasks from MongoDB',
    schedule='0 0 * * *',  # Run at midnight every day
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['monitoring', 'email'],
) as dag:

    from airflow.operators.python import PythonOperator

    generate_report = PythonOperator(
        task_id='generate_html_report',
        python_callable=generate_html_report,
        
    )

    send_email_task = PythonOperator(
        task_id='send_email',
        python_callable=send_email,
        
    )

    generate_report >> send_email_task