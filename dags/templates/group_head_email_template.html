<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .delayed-tasks {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 25px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-TODO {
            color: #ffc107;
            font-weight: bold;
        }
        .status-IN_PROGRESS {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-COMPLETED {
            color: #28a745;
            font-weight: bold;
        }
        .status-BLOCKED {
            color: #dc3545;
            font-weight: bold;
        }
        .group-tag {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .employee-info {
            background-color: #e7f5ff;
            padding: 8px;
            margin-bottom: 2px;
            border-radius: 4px;
        }
        .action-needed {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .group-section {
            margin-bottom: 30px;
            border-left: 4px solid #17a2b8;
            padding-left: 15px;
        }
        .employee-section {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .contact-info {
            font-size: 13px;
            color: #6c757d;
            margin-top: 3px;
        }
        .email-link {
            color: #007bff;
            text-decoration: none;
        }
        .email-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{ group_name }} Group Delayed Tasks Report</h2>
        <p>Generated on: {{ generation_time }}</p>
    </div>
    
    <div class="action-needed">
        <h3>⚠️ Action Required</h3>
        <p>Your group has {{ total_tasks }} overdue task(s) across {{ employees|length }} employee(s) that require attention. Please review and ensure these tasks are prioritized.</p>
    </div>
    
    <div class="delayed-tasks">
        <h3>Group Overdue Tasks Summary</h3>
        
        {% for employee_name, tasks in employee_tasks.items() %}
        <div class="employee-section">
            <h4>{{ employee_name }} ({{ tasks|length }} tasks)</h4>
            <table>
                <thead>
                    <tr>
                        <th>Task Name</th>
                        <th>Entity ID</th>
                        <th>Order ID</th>
                        <th>Assigned To</th>
                        <th>Status</th>
                        <th>Due Date</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>{{ task.entityType }}</td>
                        <td>{{ task.entityId }}</td>
                        <td>{{ task.orderId }}</td>
                        <td>
                            {{ task.assignedToName }}
                            <div class="contact-info">
                                <a href="mailto:{{ task.assignedToEmail }}" class="email-link">{{ task.assignedToEmail }}</a>
                            </div>
                        </td>
                        <td class="status-{{ task.status }}">{{ task.status }}</td>
                        <td>{{ task.dueDate }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endfor %}
    </div>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>If you have any questions, please contact your IT department.</p>
    </div>
</body>
</html> 