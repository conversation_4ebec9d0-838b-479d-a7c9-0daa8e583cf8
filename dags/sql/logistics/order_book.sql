 CREATE TABLE IF NOT EXISTS logistics.order_book(
    order_book_uid VARCHAR(255),
    order_book_id VARCHAR(255),
    purchase_order_number VARCHAR(255),
    purchase_order_date TIMESTAMP,
    deleted BOOLEAN,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    customer_id VARCHAR(255),
    credit_amount FLOAT,
    creditor_days INTEGER,
    start_date VARCHAR(255),
    category VARCHAR(255),
    order_type VARCHAR(255),
    po_value FLOAT,
    tax_percent FLOAT,
    test_key BOOLEAN,

    PRIMARY KEY(order_book_uid),
    FOREIGN KEY(customer_id) REFERENCES logistics.customers(customer_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.order_book_batch(
    order_book_batch_uid VARCHAR(255),
    supplier_order_book_id VARCHAR(255),
    inventory_in_order_id VARCHAR(255),
    inventory_id VARCHAR(255),
    packaging_id VARCHAR(255),
    unit_of_measure VARCHAR(255),
    product_id VARCHAR(255),
    type <PERSON><PERSON><PERSON><PERSON>(255),
    deleted BOOLEAN,
    created_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_at TIMESTAMP,
    updated_by VARCHAR(255),

    PRIMARY KEY(order_book_batch_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)
    
);
 
 CREATE TABLE IF NOT EXISTS logistics.order_book_products(
    order_book_uid VARCHAR(255),
    order_book_product_id VARCHAR(255),
    unit_of_measure VARCHAR(255),
    quantity FLOAT,
    price FLOAT,
    remarks VARCHAR(255),
    margin FLOAT,
    units INTEGER,
    product_id VARCHAR(255),
    inco_type VARCHAR(255),
    inco_country VARCHAR(255),
    inco_shipment_method VARCHAR(255),
    inco_port VARCHAR(255),
    packing_id VARCHAR(255),
    inco_place_of_delivery VARCHAR(255),
    inco_port_of_loading VARCHAR(255),
    inco_city VARCHAR(255),
    shipment_date TIMESTAMP,
    delivery_date TIMESTAMP,
    expected_delivery_date TIMESTAMP,
    inco_place_of_pickup VARCHAR(255),
    hs_code VARCHAR(255),

    PRIMARY KEY(order_book_product_id),
    FOREIGN KEY(order_book_uid) REFERENCES logistics.order_book(order_book_uid),
    FOREIGN KEY(packing_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)
);


 