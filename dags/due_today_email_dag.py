import logging
import os
import re
from collections import defaultdict
from datetime import datetime, timedelta
from functools import partial
from typing import Dict, List, Optional, Tuple

import pandas as pd
import pendulum
import pytz
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from dateutil import parser
from jinja2 import Environment, FileSystemLoader
from zoneinfo import ZoneInfo
from datetime import timezone

from config.action_mapping import create_action_mapping
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS
from config.location_mapping import location_mapping
from config.logi_steps import names_from, names_to, procure_stack_sla, sales_stack_sla, steps
from config.workflow_mapping import create_workflow_mapping, sample_flow_mapping
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from ses_hook import SESHook

# Constants
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
THRESHOLD_DATE = datetime(2025, 5, 1)
DEFAULT_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
# DEFAULT_EMAILS = ['<EMAIL>']

DEFAULT_TIMEZONES = {
    'India': 'Asia/Kolkata',
    'USA': 'America/New_York',
    'Canada': 'America/Toronto',
    'UAE': 'Asia/Dubai',
    'China': 'Asia/Shanghai'
}

INSERT_QUERY = """INSERT INTO public.email_notification_history 
                 (email_type, document_ref_id, state)
                 VALUES (:email_type, :document_ref_id, :state)"""

# Country to timezone/schedule mapping
COUNTRY_TIMEZONE_SCHEDULE = {
    "India": {"tz": "Asia/Kolkata", "cron": "30 3 * * *"},
    "USA": {"tz": "America/New_York", "cron": "0 13 * * *"},
    "Canada": {"tz": "America/Toronto", "cron": "0 13 * * *"},
    "UAE": {"tz": "Asia/Dubai", "cron": "0 5 * * *"},
    "China": {"tz": "Asia/Shanghai", "cron": "0 1 * * *"},
}


class EmailNotificationManager:
    """Manages email notifications for task due dates across multiple stacks."""
    
    def __init__(self, country: str = 'India'):
        """Initialize with country-specific settings."""
        self.country = country
        self.sales_db = None
        self.procure_db = None
        self.logi_db = None
        self.loc_map = {}
        
        self._initialize_components()
        
    def _initialize_components(self):
        """Initialize all required components."""
        self._initialize_databases()
        self._initialize_mappings()
        self._initialize_email_services()
        
    def _initialize_databases(self):
        """Set up database connections."""
        sales_params = DatabaseConfig.get_sales_stack_postgres_params()
        procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
        logi_params = DatabaseConfig.get_logi_stack_mongo_params()
        
        self.sales_db = PostgresOperations(sales_params)
        self.procure_db = PostgresOperations(procure_params)
        self.logi_db = MongoOperations(logi_params[0], logi_params[1])
        
    def _initialize_mappings(self):
        """Load all configuration mappings."""
        self.action_mapping = create_action_mapping()
        self.workflow_mapping = create_workflow_mapping()
        self._build_location_mapping()
        
    def _initialize_email_services(self):
        """Set up email template rendering and sending services."""
        self.template_env = Environment(
            loader=FileSystemLoader(TEMPLATE_DIR),
            autoescape=True
        )
        self.ses_hook = SESHook()
        self.aws_config = DatabaseConfig.get_aws_ses_params()
        self.sender_email = self.aws_config['sender_email']
        
    def _build_location_mapping(self):
        """Build location to email mapping."""
        location_data = location_mapping()
        for key, val in location_data.items():
            for dt in val:
                self.loc_map[dt.get('email')] = key

    def close_connections(self):
        """Close all database connections."""
        for db in [self.sales_db, self.procure_db, self.logi_db]:
            if db:
                db.close()

    # Main processing methods
    def process_sales_action_emails(self):
        """Process and send action emails for Sales stack."""
        self._process_stack_emails(
            task_fetcher=self.get_sales_stack_due_today_tasks,
            stack='SalesStack',
            preparer=self._prepare_sales_task_data,
            inserter=self._insert_sales_tasks
        )

    def process_procure_action_emails(self):
        """Process and send action emails for Procure stack."""
        self._process_stack_emails(
            task_fetcher=self.get_procure_stack_due_today_tasks,
            stack='ProcureStack',
            preparer=self._prepare_task_data,
            inserter=self._insert_procure_tasks
        )

    def process_logi_stack_action_emails(self):
        """Process and send action emails for Logi stack."""
        self._process_stack_emails(
            task_fetcher=self.get_logi_stack_tasks,
            stack='LogiStack',
            preparer=self._prepare_logi_task_data,
            inserter=self._insert_logi_tasks
        )

    def _process_stack_emails(self, task_fetcher, stack: str, preparer, inserter):
        """
        Generic email processing pipeline for any stack.
        
        Args:
            task_fetcher: Function to fetch tasks
            stack: Name of the stack
            preparer: Function to prepare task data
            inserter: Function to insert notification records
        """
        logging.info(f"Processing {stack} emails")
        
        tasks = task_fetcher()
        if not tasks:
            logging.warning(f"No tasks to process for {stack}")
            return
            
        prepared_tasks = preparer(tasks, stack)
        grouped_tasks = self._group_tasks_by_email(prepared_tasks)
        
        for email, tasks in grouped_tasks.items():
            if not tasks:
                continue
                
            self._send_email_notification(email, tasks, stack)
            inserter(tasks)

    # Task preparation methods
    def _prepare_task_data(self, tasks: List[Dict], stack: str) -> List[Dict]:
        """Prepare task data with all required information."""
        filtered_tasks = []
        
        for task in tasks:
            owner_type = self._get_owner_type(stack, task)
            email = task.get('bu_head_email') if owner_type == 'category_head' else task.get('owner_name')
            
            if (stack=='SaleStack',str(task.get('state')) == 'pricing_quotation_generated'
            and task.get('expiry_date')
            and task['expiry_date'].date() < datetime.now(timezone.utc).date()
        ):
                continue
            if not email:
                continue
            due_date, formatted_date, tz_info = self._calculate_due_date(
                email, 
                task.get('due_date'), 
                task.get('tat_hours'),
                task.get('timezone')
            )
            
            if self._is_due_today(due_date, tz_info):
                filtered_tasks.append({
                    **task,
                    'action_link': self._get_action_link(
                        stack, 
                        task.get('enquiry_id'),
                        owner_type
                    ),
                    'state' : str(task.get('state')).replace('_',' ').title(),
                    'action_link_name': self._get_action_name(stack, task),
                    'email': email,
                    'stack_name': stack,
                    'generation_time': self._current_utc_time(),
                    'due_date': formatted_date
                })

        return filtered_tasks

    def _prepare_sales_task_data(self, tasks: List[Dict], stack: str) -> List[Dict]:
        """Specialized preparation for Sales stack tasks."""
        return self._prepare_task_data(tasks, stack)  # Currently same as generic

    def _prepare_logi_task_data(self, tasks: List[Dict], stack: str) -> List[Dict]:
        """Prepare Logi stack task data."""
        return [{
            **task,
            'action_link': self._get_action_link(stack, task.get('enquiry_id'), 'LogiStack'),
            'owner_name': task.get('owner_name', ''),
            'sales_team_member': self._format_name_from_email(task.get('assigned_employee', {}).get('email')),
            'stack_name': stack,
            'generation_time': self._current_utc_time(),
        } for task in tasks]

    # Database operations
    def _insert_sales_tasks(self, tasks: List[Dict]):
        """Insert sales task notifications to database."""
        self._insert_tasks(tasks, 'Sales')

    def _insert_procure_tasks(self, tasks: List[Dict]):
        """Insert procure task notifications to database."""
        self._insert_tasks(tasks, 'Procure')

    def _insert_logi_tasks(self, tasks: List[Dict]):
        """Insert logi task notifications to database."""
        for task in tasks:
            notification_doc = {
                "email_type": "due_date_today",
                "document_ref_id": str(task.get('activity_id')),
                "state": task.get('state'),
                "created_at": datetime.now(ZoneInfo("Asia/Kolkata"))
            }
            self.logi_db.write_data("email_notification_history", notification_doc)

    def _insert_tasks(self, tasks: List[Dict], stack: str):
        """Generic task insertion method."""
        for data in tasks:
            insert_dict = {
                "email_type": "due_date_today",
                "document_ref_id": data.get('id'),
                "state": str(data.get('state')).replace(' ','_').lower()
            }
            db = self.sales_db if stack == 'Sales' else self.procure_db
            db.write_data(INSERT_QUERY, insert_dict)

    # Email handling
    def _send_email_notification(self, recipient: str, tasks: List[Dict], stack: str):
        """Send email notification for tasks."""
        owner_name = self._format_name_from_email(recipient)
        recipient = recipient.split(',')
        html_content = self._render_email_template(
            'due_date_today_email_template.html',
            tasks=tasks,
            stack_name=stack,
            owner_name=owner_name,
            generation_time=self._current_ist_time()
        )
        
        try:
            self.ses_hook.send_email(
                sender=self.sender_email,
                recipient= DEFAULT_EMAILS,  # TODO: Replace with actual recipient
                subject=f"{stack} - Tasks Due Today",
                html_body=html_content
            )
        except Exception as e:
            logging.error(f"Error sending email to {recipient}: {str(e)}")

    # Helper methods
    def _get_action_link(self, stack: str, enquiry_id: str, owner: str = '') -> Optional[str]:
        """Get the appropriate action link based on stack and owner."""
        url_mapping = {
            'ProcureStack': {
                'category_head': Variable.get("procure_admin_url"),
                'default': Variable.get("procure_dashboard_url")
            },
            'SalesStack': Variable.get("salesstack_url"),
            'LogiStack': Variable.get("logistack_url")
        }
        
        if stack == 'ProcureStack':
            base_url = url_mapping[stack].get('category_head' if owner == 'category_head' else 'default')
        else:
            base_url = url_mapping.get(stack)
            
        return base_url.format(enquiry_id=enquiry_id) if base_url else None

    def _calculate_due_date(self, email: str, last_updated: datetime, tat_hours: int, timezone: str = None) -> Tuple[datetime, str, str]:
        """Calculate due date considering working hours and timezone."""
        if not timezone:
            timezone = DEFAULT_TIMEZONES.get(self.country, 'Asia/Kolkata')
        tz_zone = timezone
        
        if not tat_hours:
            tat_hours = 12
            
        working_hours_added = 0
        due_time = last_updated.astimezone(ZoneInfo(tz_zone))
        
        # Round up to next hour if minutes/seconds present
        if due_time.minute > 0 or due_time.second > 0:
            due_time = (due_time + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
        
        # Add working hours only
        while working_hours_added < tat_hours:
            if self._is_working_hour(due_time):
                working_hours_added += 1
            due_time += timedelta(hours=1)
        
        formatted_time = self._format_timestamp(due_time, tz_zone)
        return due_time, formatted_time, tz_zone

    def _is_working_hour(self, dt: datetime) -> bool:
        """Check if datetime falls within working hours."""
        # Weekends
        if dt.weekday() >= 5:
            return False
            
        # Outside business hours (9AM-8PM)
        if not (9 <= dt.hour < 20):
            return False
            
        # Special cases
        if dt.weekday() == 5 and dt.hour >= 14:  # Saturday afternoon
            return False
        if dt.weekday() == 0 and dt.hour < 9:    # Monday before 9AM
            return False
            
        return True

    def _is_due_today(self, due_date: datetime, timezone_str: str) -> bool:
        """Check if due date is today in the specified timezone."""
        if not due_date:
            return False
            
        tz = pytz.timezone(timezone_str)
        return due_date.date() == datetime.now(tz).date()

    # Formatting methods
    def _format_name_from_email(self, email: str) -> str:
        """Convert email to formatted name."""
        if not email or str(email).lower() == 'nan':
            return "Team"
        return ' '.join(
            word.capitalize() 
            for word in (email or '@').replace('.', ' ').split('@')[0].split(' ')
        )

    def _format_timestamp(self, dt: datetime, tz_str: str) -> str:
        """Format datetime with timezone abbreviation."""
        tz = pytz.timezone(tz_str)
        return dt.astimezone(tz).strftime("%b %d %Y %H:%M %Z")

    def _current_utc_time(self) -> str:
        """Get current time in UTC as string."""
        return datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

    def _current_ist_time(self) -> str:
        """Get current time in IST as string."""
        return datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')

    def _localize_datetime(self, dt: datetime, tz_str: str) -> datetime:
        """Convert datetime to specified timezone."""
        if pd.isna(dt):
            return None
            
        if isinstance(dt, pd.Timestamp):
            return dt.tz_convert(tz_str) if dt.tz else dt.tz_localize('UTC').tz_convert(tz_str)
        return dt.astimezone(ZoneInfo(tz_str))

    # Task fetching methods (SQL queries remain unchanged)
    def get_procure_stack_due_today_tasks(self):
        """Get tasks from ProcureStack that require action."""
        due_today = f'''
                    WITH tat_mapping AS (
                SELECT * FROM (VALUES
                    {procure_stack_sla}
                ) AS t(from_state, to_state, action_required, tat_hours)
            ),
            state_transitions AS (
                SELECT
                    s1.enquiry_id,
                    s1.current_status AS from_state,
                    s1.created_at AS from_time,
                    tm.to_state,
                    tm.action_required,
                    tm.tat_hours,
                    (s1.created_at + (tm.tat_hours || ' hours')::interval)::date AS cl_due_date,
                    s1.created_at as due_date
                FROM tat_mapping tm 
                    inner join 
                    (
                    select current_status, id as enquiry_id, last_status_change as created_at from enquiries 
                    union all 
                    select status as current_status, enquiry_id, last_status_change as created_at from sample_requests
                    ) s1
                    ON s1.current_status = tm.from_state
                    
                    -- FROM status_changes s1
                    -- JOIN tat_mapping tm ON s1.new_state = tm.from_state
                    -- WHERE NOT EXISTS (
                    --     SELECT 1 FROM status_changes s2
                    --     WHERE s2.enquiry_id = s1.enquiry_id
                    --     AND s2.new_state = tm.to_state
                    -- )
                ),
            bu_head_emails_agg AS (
                SELECT
                    bu.id AS bu_id,
                    string_agg(DISTINCT bh.email, ', ') AS bu_head_emails
                FROM business_units bu
                JOIN profiles bh ON bu.id = bh.business_unit_id AND bh.role_code = 'bu_head'
                GROUP BY bu.id
            )
            SELECT
                e.id,
                e.enquiry_id,
                s.from_state AS state,
                s.to_state,
                s.from_time,
                s.due_date,
                e.customer_full_name AS customer_name,
                p.full_name AS procurement_poc,
                p.email AS owner_email,
                bu_head_emails_agg.bu_head_emails AS bu_head_email,
                s.action_required,
                p.country,
                p.timezone
            FROM state_transitions s
            LEFT JOIN enquiries e ON s.enquiry_id = e.id
            LEFT JOIN profiles p ON CAST(p.id AS text) = e.procurement_poc_id
            LEFT JOIN business_units bu ON e.category = bu.name
            LEFT JOIN bu_head_emails_agg ON bu.id = bu_head_emails_agg.bu_id
            LEFT JOIN email_notification_history en
                ON e.id::text = en.document_ref_id
                AND s.from_state = en.state
                AND email_type = 'due_date_today'
            WHERE
                en.id IS NULL
                AND p.country = :country
                AND e.created_at::date > '2025-05-01'
                AND s.from_state = e.current_status; 
        ''' 
        return self.procure_db.read_data(due_today, {'country': self.country})
    
    def get_sales_stack_due_today_tasks(self):
        due_today = f'''WITH tat_mapping AS (
            SELECT * FROM (VALUES
               {sales_stack_sla}
            ) AS t(from_state, to_state, action_required, tat_hours)
            ),
            -- Identify status changes per enquiry (looking at the from_state only)
            ordered_statuses AS (
            SELECT
                es.enquiry_id,
                cast(es.status as text) AS from_state,
                es.created_at AS from_time,
                qo.expiry_date
            FROM enquiry_status_history es
            left join quote_generation_details qd on es.id = qd.status_history_id
            left join quote_generation_options qo on qd.id = qo.quote_generation_id
            union all
            select enquiry_id, sample_status, changed_at, Null as expiry_date from sample_status_history
            )
            ,
             msk as (
                select au.email as head_email, e.sales_team_member, ur.category , ur.country, ur.timezone from enquiries e
                left join user_roles ur on e.category = ur.category
                left join auth.users au on ur.user_id::text = au.id::text
            )
            ,
            -- Match each state with its TAT and calculate the due date
            ask as
            (
            SELECT 
            distinct case when sr.id is null then cast(e.id as text) else sr.id end as id,
            e.enquiry_id,
            s.from_state as state,
            s.from_time,
            tm.tat_hours,
            tm.action_required,
            e.customer_full_name as customer_name,
            e.sales_team_member as owner_name,
            (s.from_time + (tm.tat_hours || ' hours')::interval)::date AS calculated_due_date,
            s.from_time as due_date,
            s.expiry_date
            FROM ordered_statuses s
            left join enquiries e on e.id = s.enquiry_id
            left join sample_requests sr on e.id = sr.enquiry_id
            JOIN tat_mapping tm
            ON s.from_state::text = tm.from_state
            WHERE NOT EXISTS (
                SELECT 1 FROM ordered_statuses s2
                WHERE s2.enquiry_id = s.enquiry_id
                AND s2.from_state = tm.to_state
        ) and e.current_status::text = s.from_state or sr.status = s.from_state and e.created_at::date > '2025-05-01'
            ) SELECT distinct a.* , msk.country, msk.timezone from ask a
            left join msk on a.owner_name = msk.sales_team_member
            LEFT JOIN email_notification_history en on a.id::text = en.document_ref_id and a.state = en.state and email_type = 'due_date_today'
            where en.id is null and msk.country = :country ;
            '''
        return self.sales_db.read_data(due_today, {'country': self.country})

    def get_logi_stack_tasks(self):
        """Get tasks from LogiStack that require action."""
        docs = self._get_logi_activities()
        po_grouped_docs = defaultdict(list)
        
        for doc in docs:
            po_id = doc.get('order_id', 'unknown')
            po_grouped_docs[po_id].append(doc)
        due_today_tasks = []
        
        for po_id, doc_list in po_grouped_docs.items():
            states_present = {str(doc.get('name', '')).lower(): doc for doc in doc_list}
            
            for from_state, to_state, sla_text in steps:
                if from_state.lower() in states_present and to_state.lower() in states_present:
                    from_doc = states_present[from_state.lower()]
                    to_doc = states_present[to_state.lower()]
                    
                    if not (from_doc.get('status') == 'COMPLETED' and to_doc.get('status') == 'TODO'):
                        continue
                    owner_name = self._format_name_from_email(from_doc.get('employee_email'))
                    due_dt = from_doc.get("lastUpdatedAt")
                    tat_hours = self._parse_sla_to_hours(sla_text)
                    if 'mrd' in sla_text.lower():
                        if '-' in sla_text.lower():
                            tat_hours = -tat_hours
                        elif '+' in sla_text.lower():
                            tat_hours = tat_hours
                        else:
                            tat_hours = 0
                        due_dt = from_doc.get("mrd")

                    if tat_hours is not None:
                        due_dt, formatted_timestamp, tz_info = self._calculate_due_date(
                            from_doc.get('employee_email'),
                            due_dt, 
                            tat_hours,
                            from_doc.get('timezone', 'Asia/Kolkata')

                        )
                        if self._is_due_today(due_dt, tz_info):
                            task_dict = {
                                "state": from_state.replace('_', ' ').title() if '_' in from_state else from_state,
                                "customer_name": from_doc.get('customer_name', ''),
                                "enquiry_id": from_doc.get('orderBookId', ''),
                                "order_id": from_doc.get('orderId', ''),
                                "owner_name": owner_name,
                                "createdAt": from_doc.get("createdAt"),
                                "due_date": formatted_timestamp,
                                "generation_time": self._current_utc_time(),
                                "sla_duration": sla_text,
                                "from_state": from_state,
                                "to_state": to_state,
                                'email': from_doc.get('employee_email', ''),
                                'action_link_name': sample_flow_mapping.get(f"{from_state}  {to_state}", 'No action required')
                            }
                            due_today_tasks.append(task_dict)
                            
        return due_today_tasks

    def _get_logi_activities(self) -> List[Dict]:
        collection = self.logi_db.get_collection("activity")

        name_regex = [{"name": {"$regex": name, "$options": "i"}} for name in names_from + names_to]
    
        pipeline = [
                {
                    "$match": {
                        "lastUpdatedAt": {"$gt": THRESHOLD_DATE},
                        "$or": [
                            {"name": {"$regex": name, "$options": "i"}}
                            for name in names_from + names_to
                        ],
                        "status": {"$in": ["COMPLETED", "TODO"]}
                    }
                },
                {
                    "$lookup": {
                        "from": "email_notification_history",
                        "let": {
                            "order_id_str": "$orderId",
                            "activity_name": "$name"
                        },
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$email_type", "due_date_today"]},
                                            {"$eq": ["$document_ref_id", "$$order_id_str"]},
                                            {"$eq": ["$state", "$$activity_name"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        "as": "notification_info"
                    }
                },
                {
                    "$match": {
                        "notification_info": {"$eq": []}
                    }
                },
                {
                    '$lookup': {
                        'from': 'orderBook',
                        'localField': 'secondaryId',
                        'foreignField': 'purchaseOrderNumber',
                        'as': 'order_data'
                    }
                },
                 {
                        '$lookup': {
                            'from': 'employee', 
                            'let': {
                                'assigned_to': '$assignedTo', 
                                'country_filter': self.country
                            }, 
                            'pipeline': [
                                {
                                    '$match': {
                                        '$expr': {
                                            '$and': [
                                                {
                                                    '$eq': [
                                                        {
                                                            '$toString': '$_id'
                                                        }, '$$assigned_to'
                                                    ]
                                                }, {
                                                    '$or': [
                                                        {
                                                            '$eq': [
                                                                '$country', '$$country_filter'
                                                            ]
                                                        }, {
                                                            '$eq': [
                                                                '$address.country', '$$country_filter'
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    '$ne': [
                                                        '$deleted', True
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                }
                            ], 
                            'as': 'assigned_employee'
                        }
                    }, 
                {
                    "$lookup": {
                        "from": "supplierOrderBook",
                        "let": {
                "orderBookId": { "$arrayElemAt": ["$order_data.orderBookId", 0] }
            },
            "pipeline": [
                {
                    "$match": {
                        "$expr": {
                            "$eq": ["$linkedCOBId", "$$orderBookId"]
                        }
                    }
                }
            ],
            "as": "supplier_order_data"
        }
    },
                {
                    "$project": {
                        "name": 1,
                        "entityId": 1,
                        "orderId": 1,
                        "secondaryId": 1,
                        "status": 1,
                        "dueDate": 1,
                        "lastUpdatedAt": 1,
                        "assignedTo": 1,
                        "customerName": 1,
                        "orderBookId": { "$arrayElemAt": ["$order_data._id", 0] },
                        "employee_email": { "$arrayElemAt": ["$assigned_employee.email", 0] },
                        "employee_name": { "$arrayElemAt": ["$assigned_employee.name", 0] },
                        "mrd": { "$arrayElemAt": ["$supplier_order_data.mrd", 0] },
                        "supplierName": "$supplier_order_data.supplier.name",
                        "supplierEmail": "$supplier_order_data.supplier.email",
                        "timezone": { "$arrayElemAt": ["$assigned_employee.timezone", 0] },
                    }
                }
            ]
        return list(collection.aggregate(pipeline))



    def _parse_sla_to_hours(self, sla_text: str) -> Optional[int]:
        """Parse SLA text to hours."""
            
        sla_text = sla_text.strip().lower()
        
        if "hours" in sla_text or "hrs" in sla_text:
            match = re.search(r"(\d+)", sla_text)
            return int(match.group(1)) if match else None
        elif "days" in sla_text:
            match = re.search(r"(\d+)(-(\d+))?", sla_text)
            if match:
                upper = match.group(3) if match.group(3) else match.group(1)
                return int(upper) * 24
        return None

    def _group_tasks_by_email(self, tasks: List[Dict]) -> Dict[str, List[Dict]]:
        """Group tasks by owner email."""
        tasks_by_email = defaultdict(list)
        for task in tasks:
            if 'email' in task:
                tasks_by_email[task['email']].append(task)
        return tasks_by_email

    def _render_email_template(self, template_name: str, **kwargs) -> str:
        """Render email template with given context."""
        template = self.template_env.get_template(template_name)
        return template.render(**kwargs)

    def _get_owner_type(self, stack: str, task: Dict) -> str:
        """Determine owner type for a task."""
        transitions = self.workflow_mapping.get(stack, {}).get('transitions', {})
        return transitions.get(f"{task.get('state', '')}_to_{task.get('to_state', '')}", {}).get('owner', '')

    def _get_action_name(self, stack: str, task: Dict) -> str:
        """Get action name for a task."""
        states = self.workflow_mapping.get(stack, {}).get('states', {})
        return states.get(task.get('state', {}), {}).get('action_required', 'No action required')


# DAG creation functions
def create_country_dag(country: str, config: Dict):
    """Create a DAG for a specific country."""
    local_tz = pendulum.timezone(config["tz"])
    dag_id = f"due_date_email_dag_{country.lower()}"
    
    dag = DAG(
        dag_id,
        default_args=DEFAULT_DAG_ARGS,
        description=f'Send email notifications for {country}',
        schedule=config["cron"],
        catchup=False,
        start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
        tags=['email', 'notifications', country],
        dagrun_timeout=timedelta(minutes=15),
    )
    
    with dag:
        # Create tasks for each email type
        tasks = {
            task_name: PythonOperator(
                task_id=f"{task_name}_{country.lower()}",
                python_callable=partial(execute_email_task, country, method_name),
                execution_timeout=timedelta(minutes=5),
            )
            for task_name, method_name in [
                ('sales_action', 'process_sales_action_emails'),
                ('procure_action', 'process_procure_action_emails'),
                ('logi_stack_action', 'process_logi_stack_action_emails')
            ]
        }
        
        # Set task dependencies
        tasks['sales_action'] >> tasks['procure_action'] >> tasks['logi_stack_action']
    
    return dag

def execute_email_task(country: str, method_name: str):
    """Execute a specific email task method for a country."""
    manager = EmailNotificationManager(country)
    try:
        getattr(manager, method_name)()
    finally:
        manager.close_connections()

# Create DAGs for all countries
for country, config in COUNTRY_TIMEZONE_SCHEDULE.items():
    dag = create_country_dag(country, config)
    globals()[dag.dag_id] = dag