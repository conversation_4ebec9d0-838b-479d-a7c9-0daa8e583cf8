 CREATE TABLE IF NOT EXISTS logistics.product(
    product_uid VARCHAR(255),
    product_id VARCHAR(255),
    trade_name VA<PERSON><PERSON><PERSON>(255),
    grade VARCHAR(255),
    technical_name VARCHAR(255),
    functions VA<PERSON><PERSON>R(255),
    family VARCHAR(255),
    deleted BO<PERSON>EAN,
    cas_number VA<PERSON><PERSON><PERSON>(255),
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    sub_category VARCHAR(255),
    category VARCHAR(255),

    PRIMARY KEY(product_uid)
);

CREATE TABLE IF NOT EXISTS logistics.product_batch(
    product_batch_uid VARCHAR(255),
    units INTEGER,
    supplier_order_dispatch_id VARCHAR(255),
    customer_order_dispatch_id VARCHAR(255),
    net_weight_per_unit FLOAT,
    packaging_id VARCHAR(255),
    unit_of_measure VARCHAR(255),
    product_id VARCHAR(255),
    type VA<PERSON>HA<PERSON>(255),
    created_at TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted <PERSON><PERSON><PERSON><PERSON><PERSON>,
    updated_at TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),

    <PERSON><PERSON><PERSON><PERSON>(product_batch_uid),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY(product_id) REFERENCES logistics.product(product_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid)
);