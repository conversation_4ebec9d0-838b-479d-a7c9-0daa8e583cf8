CREATE TABLE IF NOT EXISTS logistics.activity (
    activity_id VARCHAR(50),
    entity_type VARCHAR(50),
    entity_id VARCHAR(50),
    order_id VARCHAR(50),
    secondary_id VARCHAR(50),
    name VA<PERSON>HAR(255),
    description TEXT,
    assigned_to <PERSON><PERSON><PERSON><PERSON>(50),
    due_date TIMESTAMP,
    due_date_logic VARCHAR(50),
    deleted BOOLEAN DEFAULT FALSE,
    category VARCHAR(50),
    status VARCHAR(20),
    created_by VA<PERSON>HA<PERSON>(50),
    created_at TIMESTAMP,
    group_name VARCHAR(50),
    task_id VARCHAR(20),
    dependent_on VARCHAR(20),
    dependency_resolved BOOLEAN DEFAULT FALSE,
    order_type VARCHAR(20),
    on_hold BOOLEAN DEFAULT FALSE,
    customer_name VA<PERSON>HAR(255),
    product_name VA<PERSON>HA<PERSON>(255),

    PRIMARY KEY (activity_id)
);