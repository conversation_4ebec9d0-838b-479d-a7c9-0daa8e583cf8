<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLA Violation Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fdfdfd;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            color: #333;
        }
        .alert {
            background-color: #fff3cd;
            padding: 15px 20px;
            border-left: 5px solid #ffc107;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .urgent h3 {
            margin-top: 0;
            color: #dc3545;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: #fff;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-TODO {
            color: #ffc107;
            font-weight: bold;
        }
        .status-IN_PROGRESS {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-COMPLETED {
            color: #28a745;
            font-weight: bold;
        }
        .status-BLOCKED {
            color: #dc3545;
            font-weight: bold;
        }
        .group-tag {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .action-link {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>

    <div class="header">
        <h2>{{ stack_name }} | SLA Violation</h2>
        <p>Dear {{ owner_name }},</p>
    </div>

    <!-- <div class="alert">
        ⚠️ <strong>Immediate Action Required:</strong> You have {{ tasks|length }} overdue task(s) in <strong>{{ stack_name }}</strong>. Please take prompt action to avoid further SLA breaches.
    </div> -->

    <div class="urgent">
        <h3>Urgent: Overdue Tasks in {{stack_name}}</h3>
        <table>
            <thead>
                <tr>
                    <th>Action</th>
                    <th>PO/ENQ Number</th>
                    <th>Customer Name</th>
                    <th>State</th>
                    <th>Due Date & Time</th>
                    <th>Overdue By</th>
                    
                </tr>
            </thead>
            <tbody>
                {% for dn, tks in tasks.items() %}
                    {% for task in tks %}
                    <tr>
                        <td><a href="{{ task.action_link }}" class="action-link">{{task.action_link_name}}</a></td>
                        <td>{{ task.enquiry_id }}</td>
                        <td>{{ task.customer_name }}</td>
                        <td class="status-{{ task.state | replace(' ', '_') }}">{{ task.state }}</td>
                        <td>{{ task.due_date }}</td>
                        <td>{{ task.over_due }} hrs</td>
                    
                    </tr>
                    {% endfor %}
                    <br><br>
                    <br><br>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <p>Please take the required action now to resolve the tasks and prevent further delays. Your prompt attention is appreciated.</p>

    <p>Best Regards,<br><strong>Team Mstack</strong></p>

    <div class="footer">
        <p>This is an automated message generated at {{ generation_time }}. Please do not reply to this email.</p>
    </div>

</body>
</html>
