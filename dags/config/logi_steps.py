steps = [
            ("Sample Request Received", "Sample Request No (SR No.) generation", "12 hours"), 
            ("Sample Request No (SR No.) generation", "Supplier finalization", "24 hrs"),
            ("Supplier finalization", "Sample production complete", "72 hrs"),
            ("Sample production complete", "Route Selection", "mrd date - 12 hrs"),
            ("Route Selection", "Sample dispatch from Supplier", "mrd date + 12 hrs"),
            ("Sample dispatch from Supplier", "Origin Partner receiving the samples", "3-6 days"),
            ("Origin Partner receiving the samples", "Testing completed", "12 hrs"),
            ("Testing completed", "Mstack Documentation", "12 hrs"),
            ("Origin Partner receiving the samples", "Repackaging - Relabelling -Repouring at Origin", "24 hrs"),
            ("Repackaging - Relabelling -Repouring at Origin", "Approval for movement", "12 hrs"),
            ("Approval for movement", "Dispatch to Destination Partner", "12 hrs"),
            ("Dispatch to Destination Partner", "Delivery at Destination Partner", "7 days"),
            ("Delivery at Destination Partner", "Repackaging - Relabelling -Repouring at Destination", "12 hrs"),
            ("Repackaging - Relabelling -Repouring at Destination", "Approval before dispatching to Customer", "12 hrs"),
            ("Approval before dispatching to Customer", "Dispatch to Customer", "12 hrs"),
            ("Dispatch to Customer", "Delivery Confirmation", "24 hrs"),
            ("PO receipt", "PO Generation", "12 hours"),
            ("PO Generation", "CDO Generation", "24 hrs"),
            ("CDO Generation", "Supplier RFQ", "48 hrs"),
            ("Supplier RFQ", "Margin Approval", "24 hrs"),
            ("Margin Approval", "Supplier PO", "12 hrs"),
            ("Supplier PO", "Advance to Supplier", "24 hrs"),
            ("Supplier PO", "Booking confirmation", "mrd - 60 hrs"),
            ("Booking confirmation", "Sent For Testing", "mrd date"),
            ("Sent For Testing", "Testing Results Received", "72 hrs"),
            ("Testing Results Received", "Packaging/Labelling", "12 hrs"),
            ("Packaging/Labelling", "Documentation", "12 hrs"),
            ("Documentation", "QC Approval", "12 hrs"),
            ("QC Approval", "Loading container in factory", "24 hrs"),
            ("Loading container in factory", "Final release of the cargo from the supplier", "12 hrs"),
            ("Final release of the cargo from the supplier", "Loading of cargo at CFS", "7 days"),
            ("Loading of cargo at CFS", "Custom Release", "72 hrs"),
            ("Custom Release", "Appointment with customer for LM", "24 hrs"),
            ("Appointment with customer for LM", "Delivery Confirmation", "48 hrs"),
            ("Delivery Confirmation", "Send final invoice to the customer", "12 hrs"),
        ]

procure_stack_sla = """
    ('enquiry_received', 'enquiry_assigned', 'assign enquiry', 12),
            ('enquiry_assigned', 'clarification_needed', 'find suppliers/ request clarification', 24),
            ('clarification_needed', 'supplier_identified', 'find suppliers/ request clarification', 48),
            ('suppliers_identified', 'quote_requested', 'send quote_request_to_supplier', 12),
            ('quote_requested', 'quote_received', 'confirm_quote received from supplier', 48),
            ('quote_received', 'quote_prepared', 'prepare_quotation_&_send_for_approval', 12),
            ('quote_prepared', 'quote_approved', 'approve/reject_quote', 12),
            ('revision_needed', 'quote_prepared', 'revise_quote_&_send_for_approval', 12),
            ('quote_approved', 'pricing_quotation_generated', 'check_&_send_final_quotation_for_customer', 12),
            ('sample_request_received_from_sales', 'sample_request_raised_to_supplier', 'initiate_sample_request_to_supplier', 12),
            ('sample_request_raised_to_supplier', 'sample_ready', 'initiate_sample_movement_on_logistack', 168),
            ('sample_ready', 'in-transit_for_testing', 'initiate_sample_dispatch', 12)
"""


sales_stack_sla = """
    ('clarification_needed', 'enquiry_assigned', 'Respond to the clarification query', 12),
                ('pricing_quotation_generated', 'quotation_feedback', 'Fill customer feedback on the quotation', 48),
                ('quote_accepted', 'sample_requested', 'Get a sample request/ PO from the customer', 72),
                ('sample_delivered', 'delivery_confirmation', 'Confirm sample delivery with the customer', 48),
                ('delivery_confirmation', 'testing_started_confirmation', 'Confirm if customer started testing the sample', 360),
                ('testing_started_confirmation', 'sample_feedback', 'Fill customer feedback on the sample', 720),
                ('sample_accepted', 'po_raised', 'Get a PO from the customer', 48)
"""


from_state_logi = [
    "sample request received",
            "sample request no (sr no.) generation",
            "Supplier finalization",
            "sample production complete",
            "route selection", 
            "sample dispatched from supplier",
            "origing partner receiving samples",
            "testing completed",
            "repackaging - relabelling -repouring at origin",
            "approval for movement",
            "dispatched to destination partner",
            "delivery at destination partner",
            "repackaging - relabelling -repouring at destination",
            "approval before dispatch to customer",
            "dispatch to customer",
            "po receipt",
            "po generation",
            "cdo generation", 
            "supplier rfq",
            "margin approval",
            "supplier po",
            "booking confirmation",
            "sent for testing",
            "testing results received",
            "packaging/labelling",
            "documentation",
            "qc approval",
            "loading container in factory",
            "final release of the cargo from the supplier",
            "loading of cargo at cfs",
            "custom release",
            "appointment with customer for lm",
            "delivery confirmation"
]




names_from = [
                "Sample Request Received",
                "Sample Request generation",
                "Supplier finalization",
                "Sample production complete", 
                "Route Selection",
                "Sample dispatch from Supplier",
                "Origin Partner receiving the samples",
                "Testing completed",
                "Repackaging - Relabelling -Repouring at Origin",
                "Approval for movement",
                "Dispatch to Destination Partner",
                "Delivery at Destination Partner",
                "Repackaging - Relabelling -Repouring at Destination",
                "Approval before dispatching to Customer",
                "Dispatch to Customer",
                "PO receipt",
                "PO Generation",
                "CDO Generation",
                "Supplier RFQ",
                "Margin Approval", 
                "Supplier PO",
                "Booking confirmation",
                "Sent For Testing",
                "Testing Results Received",
                "Packaging/Labelling",
                "Documentation",
                "QC Approval",
                "Loading container in factory",
                "Final release of the cargo from the supplier",
                "Loading of cargo at CFS",
                "Custom Release",
                "Appointment with customer for LM",
                "Delivery Confirmation"
            ]


names_to = [
                "Sample Request No (SR No.) generation",
                "Supplier Finalization",
                "Payment to supplier",
                "Sample production complete",
                "Route Selection", 
                "Sample dispatch from Supplier",
                "Origin Partner receiving the samples",
                "Testing completed",
                "Mstack Documentation",
                "Repackaging - Relabelling -Repouring at Origin",
                "Approval for movement",
                "Dispatch to Destination Partner",
                "Delivery at Destination Partner",
                "Repackaging - Relabelling -Repouring at Destination",
                "Approval before dispatching to Customer",
                "Dispatch to Customer",
                "Delivery Confirmation",
                "PO Generation",
                "CDO Generation",
                "Supplier RFQ",
                "Margin Approval",
                "Supplier PO",
                "Advance to Supplier",
                "Booking confirmation",
                "Sent For Testing",
                "Testing Results Received",
                "Packaging/Labelling",
                "Documentation", 
                "QC Approval",
                "Loading container in factory",
                "Final release of the cargo from the supplier",
                "Loading of cargo at CFS",
                "Custom Release",
                "Appointment with customer for LM",
                "Delivery Confirmation",
                "Send final invoice to the customer"
            ]
