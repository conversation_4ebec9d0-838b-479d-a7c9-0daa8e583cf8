def create_workflow_mapping():
    """
    Creates a comprehensive mapping for workflow transitions, actions, and SLAs
    across all stacks (ProcureStack, SalesStack, LogiStack).
    
    This mapping is used for:
    1. Determining valid state transitions
    2. Assigning task owners
    3. Setting TAT (Turn Around Time) for SLA calculations
    4. Generating action-required notifications
    5. Tracking SLA violations for escalation
    """
    mapping = {}
    
    # ProcureStack workflow mapping
    mapping['ProcureStack'] = {
        'owner_role': 'procurement_poc',
        'transitions': {
            'enquiry_received_to_enquiry_assigned': {
                'from': 'enquiry_received',
                'to': 'enquiry_assigned',
                'action_required': 'Assign Enquiry',
                'owner': 'category_head',
                'tat_hours': 12
            },
            'enquiry_assigned_to_clarification_needed': {
                'from': 'enquiry_assigned',
                'to': 'clarification_needed',
                'action_required': 'Find Suppliers/Request Clarification',
                'owner': 'procurement_executive',
                'tat_hours': 24
            },
            'enquiry_assigned_to_suppliers_identified': {
                'from': 'enquiry_assigned',
                'to': 'suppliers_identified',
                'action_required': 'Find Suppliers',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'clarification_needed_to_suppliers_identified': {
                'from': 'clarification_needed',
                'to': 'suppliers_identified',
                'action_required': 'Find Suppliers',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'clarification_needed_to_regret': {
                'from': 'clarification_needed',
                'to': 'regret',
                'action_required': 'Find Suppliers/Request Clarification',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'suppliers_identified_to_quote_requested': {
                'from': 'suppliers_identified',
                'to': 'quote_requested',
                'action_required': 'Send Quote Request to Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_requested_to_quote_received': {
                'from': 'quote_requested',
                'to': 'quote_received',
                'action_required': 'Confirm Quote Received from Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'quote_received_to_quote_prepared': {
                'from': 'quote_received',
                'to': 'quote_prepared',
                'action_required': 'Prepare Quotation & Send for Approval',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_prepared_to_quote_approved': {
                'from': 'quote_prepared',
                'to': 'quote_approved',
                'action_required': 'Approve/Reject Quote',
                'owner': 'category_head',
                'tat_hours': 12
            },
            'quote_prepared_to_quote_revision_needed': {
                'from': 'quote_prepared',
                'to': 'quote_revision_needed',
                'action_required': 'Approve/Reject Quote',
                'owner': 'category_head',
                'tat_hours': 12
            },
            'quote_revision_needed_to_quote_prepared': {
                'from': 'quote_revision_needed',
                'to': 'quote_prepared',
                'action_required': 'Revise Quote & Send for Approval',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_approved_to_pricing_quotation_generated': {
                'from': 'quote_approved',
                'to': 'pricing_quotation_generated',
                'action_required': 'Check & Send Final quotation for customer',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'sample_request_received_to_sample_request_raised': {
                'from': 'sample_request_received',
                'to': 'sample_request_raised',
                'action_required': 'Initiate Sample request to Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'sample_request_raised_to_sample_ready': {
                'from': 'sample_request_raised',
                'to': 'sample_ready',
                'action_required': 'Initiate Sample Movement on LogiStack',
                'owner': 'procurement_executive',
                'tat_hours': 168  # 7 days
            },
            'sample_ready_to_in_transit_for_testing': {
                'from': 'sample_ready',
                'to': 'in_transit_for_testing',
                'action_required': 'Initiate Sample Dispatch',
                'owner': 'logistics_executive',
                'tat_hours': 12
            }
        },
        'states': {
            'enquiry_received': {
                'action_required': 'Assign Enquiry',
                'owner': 'category_head',
                'tat_hours': 12
            },
            'enquiry_assigned': {
                'action_required': 'Find Suppliers/ Request Clarification',
                'owner': 'procurement_executive',
                'tat_hours': 24
            },
            'clarification_needed': {
                'action_required': 'Find Suppliers/Request Clarification',
                'owner': 'procurement_executive',
                'tat_hours': 24
            },
            'suppliers_identified': {
                'action_required': 'Send Quote Request to Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_requested': {
                'action_required': 'Confirm Quote Received from Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'quote_received': {
                'action_required': 'Prepare Quotation & Send for Approval',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_prepared': {
                'action_required': 'Approve/Reject Quote',
                'owner': 'category_head',
                'tat_hours': 12
            },
            'revision_needed': {
                'action_required': 'Revise Quote & Send for Approval',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'quote_approved': {
                'action_required': 'Check & Send Final quotation for customer',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'pricing_quotation_generated': {
                'action_required': 'Wait for customer feedback',
                'owner': 'procurement_executive',
                'tat_hours': 48
            },
            'quote_redo': {
                'action_required': 'Prepare Quotation basis feedback',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'sample_request_received': {
                'action_required': 'Initiate Sample request to Supplier',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'sample_request_raised': {
                'action_required': 'Initiate Sample Movement on LogiStack',
                'owner': 'procurement_executive',
                'tat_hours': 168  # 7 days
            },
            'sample_ready': {
                'action_required': 'Initiate Sample Dispatch',
                'owner': 'logistics_executive',
                'tat_hours': 12
            },
            'po_raised': {
                'action_required': 'Create Order on LogiStack',
                'owner': 'procurement_executive',
                'tat_hours': 12
            },
            'regret': {
                'action_required': 'No action required',
                'owner': 'procurement_executive',
                'tat_hours': 0
            }
        }
    }
    
    # SalesStack workflow mapping
    mapping['SalesStack'] = {
        'owner_role': 'sales_poc',
        'transitions': {
            'clarification_needed_to_clarification_answered': {
                'from': 'clarification_needed',
                'to': 'clarification_answered',
                'action_required': 'Respond to the clarification query',
                'owner': 'sales_poc',
                'tat_hours': 12
            },
            'pricing_quotation_generated_to_quotation_feedback': {
                'from': 'pricing_quotation_generated',
                'to': 'quotation_feedback',
                'action_required': 'Fill customer feedback on the quotation',
                'owner': 'sales_poc',
                'tat_hours': 48
            },
            'quote_accepted_to_sample_requested': {
                'from': 'quote_accepted',
                'to': 'sample_requested',
                'action_required': 'Get a sample request/ PO from the customer',
                'owner': 'sales_poc',
                'tat_hours': 72
            },
            'sample_delivered_to_delivery_confirmation': {
                'from': 'sample_delivered',
                'to': 'delivery_confirmation',
                'action_required': 'Confirm sample delivery with the customer',
                'owner': 'sales_poc',
                'tat_hours': 24
            },
            'delivery_confirmation_to_testing_started_confirmation': {
                'from': 'delivery_confirmation',
                'to': 'testing_started_confirmation',
                'action_required': 'Confirm if customer started testing the sample',
                'owner': 'sales_poc',
                'tat_hours': 360  # 15 days (default, will be dynamically calculated)
            },
            'testing_started_confirmation_to_sample_feedback': {
                'from': 'testing_started_confirmation',
                'to': 'sample_feedback',
                'action_required': 'Fill customer feedback on the sample',
                'owner': 'sales_poc',
                'tat_hours': 720  # 30 days (default, will be dynamically calculated)
            },
            'sample_accepted_to_po_raised': {
                'from': 'sample_accepted',
                'to': 'po_raised',
                'action_required': 'Get a PO from the customer',
                'owner': 'sales_poc',
                'tat_hours': 48
            }
        },
        'states': {
            'clarification_needed': {
                'action_required': 'Respond to the clarification query',
                'owner': 'sales_poc',
                'tat_hours': 12
            },
            'pricing_quotation_generated': {
                'action_required': 'Fill customer feedback on the quotation',
                'owner': 'sales_poc',
                'tat_hours': 48
            },
            'quote_accepted': {
                'action_required': 'Get a sample request/ PO from the customer',
                'owner': 'sales_poc',
                'tat_hours': 72
            },
            'sample_delivered': {
                'action_required': 'Confirm sample delivery with the customer',
                'owner': 'sales_poc',
                'tat_hours': 24
            },
            'delivery_confirmation': {
                'action_required': 'Confirm if customer started testing the sample',
                'owner': 'sales_poc',
                'tat_hours': 360  # 15 days (default)
            },
            'testing_started_confirmation': {
                'action_required': 'Fill customer feedback on the sample',
                'owner': 'sales_poc',
                'tat_hours': 720  # 30 days (default)
            },
            'sample_accepted': {
                'action_required': 'Get a PO from the customer',
                'owner': 'sales_poc',
                'tat_hours': 48
            }
        }
    }
    
        # LogiStack workflow mapping - Sample Flow
    mapping['LogiStack_Sample'] = {
        'owner_role': 'logistics_poc',
        'states': {
            "Sample Request Received": {
                "action_required": "Sample Order Creation on LogiStack"
            },
            "Sample Request generation": {
                "action_required": "Identify & Finalize Supplier"
            },
            "Supplier finalization": {
                "action_required": "If required, payment to Supplier"
            },
            "Sample production complete": {
                "action_required": "Confirm Production Complete"
            },
            "Route Selection": {
                "action_required": "Finalize Route for Sample movement"
            },
            "Sample dispatch from Supplier": {
                "action_required": "Initiate Sample dispatch"
            },
            "Origin Partner receiving the samples": {
                "action_required": "Confirm Sample received at Origin Partner"
            },
            "Testing completed": {
                "action_required": "Upload Test Results"
            },
            "Repackaging - Relabelling -Repouring at Origin": {
                "action_required": "Repack before Dispatch"
            },
            "Approval for movement": {
                "action_required": "Approval for Movement"
            },
            "Dispatch to Destination Partner": {
                "action_required": "Initiate Sample dispatch to destination"
            },
            "Delivery at Destination Partner": {
                "action_required": "Confirm delivery at Destination partner"
            },
            "Repackaging - Relabelling -Repouring at Destination": {
                "action_required": "Repack before final Dispatch to customer"
            },
            "Approval before dispatching to Customer": {
                "action_required": "Approval for delivery to customer"
            },
            "Dispatch to Customer": {
                "action_required": "Initiate last mile delivery to customer"
            },
            "PO receipt": {
                "action_required": "Confirm PO receipt"
            },
            "PO Generation": {
                "action_required": "Create PO"
            },
            "CDO Generation": {
                "action_required": "Generate CDO"
            },
            "Supplier RFQ": {
                "action_required": "Send RFQ to Supplier"
            },
            "Margin Approval": {
                "action_required": "Approve Margins"
            },
            "Supplier PO": {
                "action_required": "Share PO with Supplier"
            },
            "Booking confirmation": {
                "action_required": "Confirm Booking"
            },
            "Sent For Testing": {
                "action_required": "Send Sample for Testing"
            },
            "Testing Results Received": {
                "action_required": "Review Testing Results"
            },
            "Packaging/Labelling": {
                "action_required": "Complete Packaging and Labelling"
            },
            "Documentation": {
                "action_required": "Complete Documentation"
            },
            "QC Approval": {
                "action_required": "Get QC Approval"
            },
            "Loading container in factory": {
                "action_required": "Load Container at Factory"
            },
            "Final release of the cargo from the supplier": {
                "action_required": "Release Cargo from Supplier"
            },
            "Loading of cargo at CFS": {
                "action_required": "Load Cargo at CFS"
            },
            "Custom Release": {
                "action_required": "Get Custom Release"
            },
            "Appointment with customer for LM": {
                "action_required": "Schedule LM Appointment with Customer"
            },
            "Delivery Confirmation": {
                "action_required": "Confirm sample delivered to customer"
            }
        }

    }

    
    # Role to owner mapping
    mapping['RoleToOwner'] = {
        'Sales Manager': 'sales_poc',
        'Sales Executive': 'sales_poc',
        'Business Unit Admin': 'bu_admin',
        'Category Head': 'category_head',
        'Procurement Manager': 'procurement_poc',
        'Procurement Executive': 'procurement_executive',
        'Logistics Manager': 'logistics_poc',
        'Logistics Executive': 'logistics_executive',
        'Finance Manager': 'finance_poc',
        'Finance Executive': 'finance',
        'Business Head': 'business_head',
        'Quality Head': 'quality_head'
    }
    
    # Owner to email group mapping
    mapping['OwnerToGroup'] = {
        'sales_poc': '<EMAIL>',
        'bu_admin': '<EMAIL>',
        'category_head': '<EMAIL>',
        'procurement_poc': '<EMAIL>',
        'procurement_executive': '<EMAIL>',
        'logistics_poc': '<EMAIL>',
        'logistics_executive': '<EMAIL>',
        'finance_poc': '<EMAIL>',
        'finance': '<EMAIL>',
        'business_head': '<EMAIL>',
        'quality_head': '<EMAIL>'
    }
    
    # SLA escalation configuration
    mapping['SLAEscalation'] = {
        'levels': {
            'level1': {
                'threshold_hours': 24,
                'recipients': ['owner']
            },
            'level2': {
                'threshold_hours': 48,
                'recipients': ['owner', 'category_head']
            },
            'level3': {
                'threshold_hours': 72, 
                'recipients': ['owner', 'category_head', 'management']
            }
        },
        'management_emails': ['<EMAIL>'],
        'email_schedule': {
            'action_required': 'immediate', 
            'due_date_today': '08:00',
            'sla_violation': '10:00' 
        }
    }
    
    # Email template configuration
    mapping['EmailTemplates'] = {
        'action_required': {
            'subject': '{stack} | Action Required',
            'template': 'action_required_email_template.html'
        },
        'due_date_today': {
            'subject': '{stacks} | Tasks Due Today',
            'template': 'due_date_today_email_template.html'
        },
        'sla_violation': {
            'subject': '{stacks} | SLA Violation',
            'template': 'sla_violation_email_template.html'
        }
    }
    return mapping


sample_flow_mapping = {
    "Sample Request Received  Sample Request No (SR No.) generation": "Sample Order Creation on LogiStack",
    "Sample Request No (SR No.) generation  Supplier finalization": "Identify & Finalize Supplier",
    "Supplier finalization  Payment to supplier": "If required, payment to Supplier",
    "Supplier finalization  Sample production complete": "Confirm Production Complete",
    "Sample production complete  Route Selection": "Finalize Route for Sample movement",
    "Route Selection  Sample dispatch from Supplier": "Initiate Sample dispatch",
    "Sample dispatch from Supplier  Origing Partner receiving samples": "Confirm Sample received at Origin Partner",
    "Origing Partner receiving samples  Testing Completed": "Upload Test Results",
    "Testing completed  Mstack Documentation": "Complete MStack Documentation for movement",
    "Origin Partner receiving the samples  Repackaging - Relabelling -Repouring at Origin": "Repack before Dispatch",
    "Repackaging - Relabelling -Repouring at Origin  Approval for movement": "Approval for Movement",
    "Approval for movement  Dispatch to Destination Partner": "Initiate Sample dispatch to destination",
    "Dispatch to Destination Partner  Delivery at Destination Partner": "Confirm delivery at Destination partner",
    "Delivery at Destination Partner  Repackaging - Relabelling -Repouring at Destination": "Repack before final Dispatch to customer",
    "Repackaging - Relabelling -Repouring at Destination  Approval before dispatching to Customer": "Approval for delivery to customer",
    "Approval before dispatching to Customer  Dispatch to Customer": "Initiate last mile delivery to customer",
    "Dispatch to Customer  Delivery Confirmation": "Confirm sample delivered to customer",
    "PO receipt  PO Generation": "PO recording in System",
    "PO Generation  CDO Generation": "Generate CDO",
    "CDO Generation  Supplier RFQ": "Complete Supplier & Logistics RFQ",
    "Supplier RFQ  Margin Approval": "Margin Approval required",
    "Margin Approval  Supplier PO": "Issue Supplier PO",
    "Supplier PO  Advance to Supplier": "If required, Payment of Advance to Supplier",
    "Supplier PO  Booking confirmation": "Logistics Bookings Required",
    "Booking confirmation  Sent For Testing": "Dispatch for Testing",
    "Sent For Testing   Testing Results Received": "Upload Testing Results",
    "Testing Results Received  Packaging/Labelling": "Complete Mstack Packaging & labelling",
    "Packaging/Labelling  Documentation": "Complete Documentation for Dispatch",
    "Documentation  QC Approval": "QC Approval required for movement",
    "QC Approval  Loading container in factory": "Confirm Stock loading at Source",
    "Loading container in factory  Final release of the cargo from the supplier": "Dispatch from source location",
    "Final release of the cargo from the supplier  Loading of cargo at CFS": "Confirm order is In-Transit",
    "Loading of cargo at CFS  Custom Release": "Custom clearance at destination port",
    "Custom Release  Appointment with customer for LM": "Initiate Last mile delivery",
    "Appointment with customer for LM  Delivery Confirmation": "Confirm delivery to customer",
    "Delivery Confirmation  Send final invoice to the customer": "Send Final Invoice to customer"
}
