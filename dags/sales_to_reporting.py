import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from dotenv import load_dotenv
import pandas as pd
import logging
import datetime
from datetime import timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from config.default import DEFAULT_DAG_ARGS
from functools import partial
from datetime import datetime, timezone
from etl.sales_etl import SalesETL

logging.basicConfig(level=logging.INFO)

load_dotenv()


dag = DAG(
    dag_id='sales_to_reporting_dag',
    default_args={**DEFAULT_DAG_ARGS},
    description='Sync data from SalesStack to PostgreSQL',
    schedule='0 * * * *',  # Runs every hour (at minute 0)
    catchup=False,
    tags=['database', 'sync', 'postgres'],
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
)

def sync_data(sync_function):
    """Generic sync function that creates MongoETL instance and calls specified sync method"""
    sales_etl = SalesETL()
    getattr(sales_etl, sync_function)()

sync_tasks = [
    ('auth_tables', 'sync_auth_tables'),
    ('sales_user_roles', 'sync_user_roles'),
    ('customer', 'sync_customer'),
    ('customer_meeting', 'sync_customer_meetings'),
    ('account_planning', 'sync_account_planning'),
    ('account_planning_history', 'sync_account_planning_history'),
    ('customer_quotations', 'sync_customer_quotations'),
    ('enquiries', 'sync_enquiries'),
    ('enquiry_clarifications', 'sync_enquiry_clarifications'),
    ('enquiry_documents', 'sync_enquiry_documents'),
    ('enquiry_status_history', 'sync_enquiry_status_history'),
    ('enquiry_quotations', 'sync_enquiry_quotations'),
    ('sample_requests', 'sync_sample_requests'),
    ('quotation', 'sync_quotation'),
    ('quotation_feedback', 'sync_quotation_feedback'),
    ('quote_generation_details', 'sync_quote_generation_details'),
    ('purchase_orders', 'sync_purchase_orders'),
    ('sample_requests_attachments', 'sync_sample_requests_attachments'),
    ('sample_feedback', 'sync_sample_feedback'),
    ('sample_status_history', 'sync_sample_status_history'),
    ('quotation_feedback_attachments', 'sync_quotation_feedback_attachments'),
    ('sample_feedback_attachments', 'sync_sample_feedback_attachments'),
    ('quote_generation_options', 'sync_quote_generation_options'),
    ('quote_generation_attachments', 'sync_quote_generation_attachments'),
    ('purchase_order_attachments', 'sync_purchase_order_attachments')
]

# Create tasks dynamically
tasks = {}
for task_name, sync_function in sync_tasks:
    tasks[task_name] = PythonOperator(
        task_id=f'sync_{task_name}',
        python_callable=partial(sync_data, sync_function),
        dag=dag,
        execution_timeout=timedelta(minutes=5),
    )





for i in range(len(sync_tasks) - 1):
    tasks[sync_tasks[i][0]] >> tasks[sync_tasks[i + 1][0]]