name: Deploy DAGs to MWAA S3

# on:
#   push:
#     branches:
#       - main
#     paths:
#       - "dags/**"  # Updated to match your DAGs folder

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to deploy"
        required: true
        default: "main"

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Sync DAGs to S3
      run: |
        aws s3 sync dags/ s3://${{ secrets.MWAA_BUCKET_NAME }}/dags/ --delete
