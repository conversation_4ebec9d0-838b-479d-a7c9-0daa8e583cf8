from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime

# Define default arguments
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 3, 5),  # DAG starts from this date
    'catchup': False,  # Prevents backfilling for past dates
}

#  Define a simple function to print a message
def hello_airflow():
    print("Hello, Airflow! Your DAG is running successfully.")

# Define DAG
with DAG(
    dag_id='hello_airflow',  # Unique DAG name
    default_args=default_args,
    schedule='@daily',  # Runs daily
    catchup=False,  # Avoids running missed schedules
) as dag:

    # Define task using PythonOperator
    hello_task = PythonOperator(
        task_id='print_hello',
        python_callable=hello_airflow
    )

    # Task execution order
    hello_task

