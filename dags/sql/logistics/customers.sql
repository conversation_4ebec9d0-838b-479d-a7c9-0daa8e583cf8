 CREATE TABLE IF NOT EXISTS logistics.customers(
    customer_uid VARCHAR(255),
    customer_id VARCHAR(255),
    customer_name VARCHAR(2555),
    email VARCHAR(2555),
    size VARCHAR(255),
    account_owner VARCHAR(255),
    categories VARCHAR(2555),
    type VA<PERSON><PERSON><PERSON>(255),
    deleted BOOLEAN,
    created_by VA<PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    street VARCHAR(9255),
    state VARCHAR(255),
    country VARCHAR(255),
    postal_code VARCHAR(255),
    city VARCHAR(255),

    PRIMARY KEY(customer_uid)
);
 
 CREATE TABLE IF NOT EXISTS logistics.customer_orders(
    customer_order_uid VARCHAR(255),
    order_id VARCHAR(255),
    purchase_order_number VARCHAR(255),
    purchase_order_date TIMESTAMP,
    status VARCHAR(255),
    deleted BOOLEAN,
    approved BOOLEAN,
    created_at TIMESTAMP,
    created_by <PERSON>RC<PERSON>R(255),
    invoice_date TIMESTAMP,
    updated_at TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    type_of_bl VARCHAR(255),
    shipment_date TIMESTAMP,
    delivery_date TIMESTAMP,
    customer_id VARCHAR(255),
    creditor_days FLOAT,
    credit_amount FLOAT,
    start_date VARCHAR(255),
    inco_type VARCHAR(255),
    inco_country VARCHAR(255),
    inco_shipment_method VARCHAR(255),
    inco_port VARCHAR(255),
    margin_percentage FLOAT,
    purchase_order_value FLOAT,
    logistics_cost_value FLOAT,
    duty_amount_value FLOAT,
    sales_order_value FLOAT,
    target_contribution_margin FLOAT,
    first_mile_logistics FLOAT,
    sea_freight FLOAT,
    last_mile_logistics FLOAT,
    destination_charges FLOAT,
    country_of_origin VARCHAR(255),
    tracking_url VARCHAR(255),

    PRIMARY KEY(customer_order_uid),
    FOREIGN KEY(customer_id) REFERENCES logistics.customers(customer_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.customer_order_products(
    customer_order_uid VARCHAR(255),
    customer_order_product_id VARCHAR(255),
    unit_of_measure VARCHAR(255),
    quantity FLOAT,
    price FLOAT,
    units FLOAT,
    status VARCHAR(255),
    quantity_per_unit FLOAT,
    per_unit_weight FLOAT,
    product_id VARCHAR(255),
    packaging_id VARCHAR(255),
    hs_code VARCHAR(255),
    district_of_origin VARCHAR(255),
    state_of_origin VARCHAR(255),
    chemstack_price FLOAT,
    per_unit_kg_value FLOAT,
    country_of_origin VARCHAR(255),

    PRIMARY KEY(customer_order_product_id, customer_order_uid),
    FOREIGN KEY(customer_order_uid) REFERENCES logistics.customer_orders(customer_order_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)
);