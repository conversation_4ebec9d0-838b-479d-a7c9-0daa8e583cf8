
 CREATE TABLE IF NOT EXISTS logistics.supplier_order_book(
    supplier_order_book_uid VARCHAR(255),
    so_order_book_id VARCHAR(255),
    purchase_order_number VARCHAR(255),
    purchase_order_date TIMESTAMP,
    billing_address VARCHAR(255),
    shipping_address VARCHAR(255),
    per_unit_weight FLOAT,
    currency_type VARCHAR(255),
    supplier_ref_number VARCHAR(255),
    mode_of_delivery VARCHAR(255),
    delivery_term VARCHAR(255),
    delivery_location VARCHAR(255),
    deleted BOOLEAN,
    created_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_at TIMESTAMP,
    updated_by VARCHAR(255),
    supplier_id VARCHAR(255),
    delivery_date TIMESTAMP,
    mrd TIMESTAMP,
    order_book_id VARCHAR(255),
    credit_amount FLOAT,
    advance_amount FLOAT,
    creditor_days int4,

    PRIMARY KEY(supplier_order_book_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid)

);


 CREATE TABLE IF NOT EXISTS logistics.supplier_order_book_products(

    supplier_order_book_uid VARCHAR(255),
    supplier_order_book_product_id VARCHAR(255),
    unit_of_measure VARCHAR(255),
    quantity FLOAT,
    price FLOAT,
    hs_code VARCHAR(255),
    margin FLOAT,
    label VARCHAR(255),
    units INTEGER,
    product_id VARCHAR(255),
    packing_id VARCHAR(255),
    inco_type VARCHAR(255),
    inco_country VARCHAR(255),
    inco_shipment_method VARCHAR(255),
    inco_port VARCHAR(255),

    PRIMARY KEY(supplier_order_book_product_id),
    FOREIGN KEY(supplier_order_book_uid) REFERENCES logistics.supplier_order_book(supplier_order_book_uid),
    FOREIGN KEY(packing_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)

);


 CREATE TABLE IF NOT EXISTS logistics.supplier_order_book_supplier_products(
    supplier_id VARCHAR(255),
    supplier_order_book_uid VARCHAR(255),
    supplier_product_uid VARCHAR(255),
    unit_of_measure VARCHAR(255),
    quantity FLOAT,
    price FLOAT,
    hs_code VARCHAR(255),
    units INTEGER,
    status VARCHAR(255),
    quantity_per_unit FLOAT,
    per_unit_weight FLOAT,
    product_id VARCHAR(255),
    packaging_id VARCHAR(255),
    chemstack_price FLOAT,
    per_unit_kg_value FLOAT,

    PRIMARY KEY(supplier_product_uid),
    FOREIGN KEY(supplier_order_book_uid) REFERENCES logistics.supplier_order_book(supplier_order_book_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)

);