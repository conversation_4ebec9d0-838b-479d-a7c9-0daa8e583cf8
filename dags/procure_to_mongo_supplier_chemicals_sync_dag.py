from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional
from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from contextlib import contextmanager
import logging
import uuid
import json

import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun

class SupplierChemicalSyncManager:
    """Manager class for syncing supplier-chemical relationships to MongoDB supplier products"""

    def __init__(self):
        self.mongo_ops: Optional[MongoOperations] = None
        self.procure_db: Optional[PostgresOperations] = None

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
            self.mongo_ops = MongoOperations(mongo_conn_string, mongo_db)
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            self.procure_db = PostgresOperations(procure_params)
            logging.info("Connected to MongoDB and PostgreSQL databases")
            yield
        finally:
            if self.mongo_ops:
                self.mongo_ops.close()
            if self.procure_db:
                self.procure_db.close()
            logging.info("Database connections closed")

    def get_mongo_product(self, chemical_id: str) -> Dict[str, Any]:
        """Fetch product from MongoDB based on chemical_id from chemicals table"""
        # First get the product_id from chemicals table
        chemical_query = """
            SELECT product_id 
            FROM chemicals 
            WHERE id = :chemical_id
        """
        chemical_result = self.procure_db.read_data(
            chemical_query,
            {"chemical_id": chemical_id}
        )
        
        if not chemical_result:
            logging.warning(f"No chemical found in chemicals table for id: {chemical_id}")
            return None
        
        product_id = chemical_result[0]['product_id']
        if not product_id:
            logging.warning(f"No product_id found for chemical_id: {chemical_id}")
            return None

        # Then fetch product from MongoDB using product_id
        product = self.mongo_ops.read_data(
            collection_name="product",
            query={"productId": product_id}
        )
        logging.info(f"Found product for product_id {product_id}: {product}")
        return product[0] if product else None

    def parse_json_string(self, json_str: Any) -> Dict[str, Any]:
        """Safely parse JSON string to dict"""
        try:
            if isinstance(json_str, str):
                 return json.loads(json_str.replace('\\"', '"'))
            return json.loads(json_str)
        except:
            return {}

    def transform_supplier_chemical(self, supplier_chemical: Dict[str, Any]) -> Dict[str, Any]:
        """Transform supplier_chemical data to MongoDB SupplierProduct format"""
        # Get corresponding product from MongoDBdfcw
        product = self.get_mongo_product(str(supplier_chemical['chemical_id']))
        if not product:
            logging.warning(f"No product found for chemical_id: {supplier_chemical['chemical_id']}")
            return None

        # Parse JSON strings
        plant_address = self.parse_json_string(supplier_chemical.get('plant_address', '{}'))
        contact_details = self.parse_json_string(supplier_chemical.get('contact_details', '{}'))
        
        # Parse packaging string to list
        packaging_str = supplier_chemical.get('packaging', '{}')
     #   packaging_list = packaging_str.strip('{}').split(',') if packaging_str else []
       
        return {
            "product": product,
            "mstackDocuments": [],  # Default empty list
            "address": {
                "addressLine1": "",
                "city": plant_address.get('city', ''),
                "country": plant_address.get('country', ''),
                "postalCode": plant_address.get('pinCode', '')
            },
            "capacityAvailable": float(supplier_chemical.get('available_capacity', 0) or 0),
            "totalCapacity": float(supplier_chemical.get('total_production_capacity', 0) or 0),
            "supplierType": "MANUFACTURER",  # Default value
            "documents": [],  # Default empty list
            "leadTime": supplier_chemical.get('production_lead_time', 0) or 0,
            "typicalOrderSize": 0,  # Default value
            "hsCode": supplier_chemical.get('hsn_code', ''),
            "hazardous": bool(supplier_chemical.get('hazard_classification', '')),
            "hazardousLevel": [supplier_chemical.get('hazard_classification', '')] if supplier_chemical.get('hazard_classification') else [],
            "certificateDocuments": [],  # Default empty list
            "exportApprovedRemarks": [],  # Default empty list
            "dutyRemarks": [str(supplier_chemical.get('incurred_duty', ''))] if supplier_chemical.get('incurred_duty') else []
        }

    def sync_supplier_chemicals(self) -> bool:
        """Sync supplier-chemical relationships from ProcureStack to MongoDB suppliers"""
        try:
            # Fetch supplier_chemicals modified since last sync
            select_query = """
                SELECT sc.*
                FROM supplier_chemicals sc
                WHERE sc.updated_at >= NOW() - INTERVAL '5 minutes'
                    OR sc.created_at >= NOW() - INTERVAL '5 minutes'
            """
            
            supplier_chemicals = self.procure_db.read_data(select_query, {"execution_time": QueryRun})
            
            if not supplier_chemicals:
                logging.info("No supplier_chemicals found to sync")
                return True
                
            logging.info(f"Found {len(supplier_chemicals)} supplier_chemicals to sync")
            
            success_count = 0
            for supplier_chemical in supplier_chemicals:
                try:
                    # Get supplier from MongoDB
                    supplier_query = """
                        SELECT supplier_id 
                        FROM suppliers 
                        WHERE id = :id
                    """
                    supplier_result = self.procure_db.read_data(
                        supplier_query,
                        {"id": supplier_chemical['supplier_id']}
                    )
                    
                    if not supplier_result:
                        logging.warning(f"No supplier found for id: {supplier_chemical['supplier_id']}")
                        continue

                    mongo_supplier_id = supplier_result[0]['supplier_id']
                    
                    # Transform supplier_chemical to MongoDB format
                    supplier_product = self.transform_supplier_chemical(supplier_chemical)
                    if not supplier_product:
                        continue

                    # Update supplier in MongoDB by appending/updating product
                    result = self.mongo_ops.update_data(
                        collection_name="supplier",
                        query={"supplierId": mongo_supplier_id},
                        update={
                            "$addToSet": {"products": supplier_product}
                        }
                    )
                    
                    logging.info(f"Updated supplier {mongo_supplier_id} with new product")
                    success_count += 1
                    
                except Exception as e:
                    logging.error(f"Error syncing supplier_chemical {supplier_chemical.get('id')}: {str(e)}")
                    continue
            
            logging.info(f"Successfully synced {success_count}/{len(supplier_chemicals)} supplier_chemicals")
            return True
            
        except Exception as e:
            logging.error(f"Error in supplier_chemical sync: {str(e)}")
            raise

def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""
    def wrapper():
        sync_manager = SupplierChemicalSyncManager()
        try:
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")
        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            raise
    return wrapper

@create_sync_task
def sync_supplier_chemicals(manager: SupplierChemicalSyncManager) -> bool:
    """Sync supplier-chemical relationships from ProcureStack to MongoDB"""
    return manager.sync_supplier_chemicals()

# Create the DAG
dag = DAG(
    'procure_to_mongo_supplier_chemicals_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
    },
    description='Sync supplier-chemical relationships from ProcureStack to MongoDB suppliers',
    schedule='*/5 * * * *',
   # or alternatively: schedule=timedelta(minutes=20),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'mongodb', 'postgres', 'supplier_chemicals'],
    dagrun_timeout=timedelta(minutes=10),
    max_active_runs=1,
)

# Create task
sync_task = PythonOperator(
    task_id='sync_supplier_chemicals',
    python_callable=sync_supplier_chemicals,
    dag=dag,
    execution_timeout=timedelta(minutes=5),
)
