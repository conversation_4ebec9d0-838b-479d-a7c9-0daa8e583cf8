from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional
from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from contextlib import contextmanager
import logging
import uuid

import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun

class SupplierSyncManager:
    """Manager class for syncing suppliers from ProcureStack to MongoDB"""

    def __init__(self):
        self.mongo_ops: Optional[MongoOperations] = None
        self.procure_db: Optional[PostgresOperations] = None

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            # Connect to MongoDB
            mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
            self.mongo_ops = MongoOperations(mongo_conn_string, mongo_db)

            # Connect to ProcureStack PostgreSQL
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            self.procure_db = PostgresOperations(procure_params)

            logging.info("Connected to MongoDB and PostgreSQL databases")
            yield

        finally:
            if self.mongo_ops:
                self.mongo_ops.close()
            if self.procure_db:
                self.procure_db.close()
            logging.info("Database connections closed")

    def generate_next_supplier_id(self) -> str:
        """Generate next supplier ID in format SP00001"""
        # all_suppliers = self.mongo_ops.read_data(
        #     collection_name="supplier",
        #     query={},
        #     projection={"supplierId": 1}
        # )
        
        # max_num = 0
        # for supplier in all_suppliers:
        #     supplier_id = supplier.get('supplierId', '')
        #     if supplier_id and supplier_id.startswith('SP'):
        #         try:
        #             num = int(supplier_id[2:])
        #             max_num = max(max_num, num)
        #         except ValueError:
        #             continue
        
        # next_num = max_num + 1
        # return f"SP{next_num:05d}"
        result = self.mongo_ops.get_collection("supplier").aggregate([
        {
            "$match": {
                "supplierId": {"$regex": "^SP\\d+$"}  # Match SP followed by numbers
            }
        },
        {
            "$project": {
                "num": {"$toInt": {"$substr": ["$supplierId", 2, -1]}}
            }
        },
        {
            "$group": {
                "_id": None,
                "maxNum": {"$max": "$num"}
            }
        }
         ])
    
        max_num = next(result, {"maxNum": 0})["maxNum"]
        return f"SP{max_num + 1:05d}"
    

    def transform_postgres_to_mongo_supplier(self, postgres_supplier: Dict[str, Any]) -> Dict[str, Any]:
        """Transform PostgreSQL supplier data to MongoDB format"""
        # Convert UUID to string for createdBy and lastUpdatedBy
        created_by = postgres_supplier.get('created_by')
        if isinstance(created_by, uuid.UUID):
            created_by = str(created_by)

        # Fetch primary contact details
        contact_query = """
            SELECT first_name, last_name, email, phone
            FROM supplier_contacts
            WHERE supplier_id = :supplier_id AND is_primary = true
            LIMIT 1
        """
        contacts = self.procure_db.read_data(
            contact_query, 
            {"supplier_id": postgres_supplier['id']}
        )
        
        # Fetch primary address details
        address_query = """
            SELECT address_line1, address_line2, city, state, country, postal_code
            FROM supplier_addresses
            WHERE supplier_id = :supplier_id AND is_primary = true
            LIMIT 1
        """
        addresses = self.procure_db.read_data(
            address_query,
            {"supplier_id": postgres_supplier['id']}
        )
        
        primary_contact = contacts[0] if contacts else {}
        primary_address = addresses[0] if addresses else {}

        # Combine address lines if both exist
        address_line1 = primary_address.get('address_line1', '')
        address_line2 = primary_address.get('address_line2')
        full_address = f"{address_line1}, {address_line2}" if address_line2 else address_line1

        return {
            "supplierId": postgres_supplier.get('supplier_id'),
            "name": postgres_supplier.get('company_name'),
            "email": primary_contact.get('email', ''),
            "mobile": primary_contact.get('phone', ''),
            "address": {
                "addressLine1": full_address,
                "city": primary_address.get('city', ''),
                "state": primary_address.get('state', ''),
                "country": primary_address.get('country', ''),
                "postalCode": primary_address.get('postal_code', '')
            },
            "products": [],  # Empty array as requested
            "deleted": False,
            "createdBy": created_by,
            "lastUpdatedBy": postgres_supplier.get('updated_by'),
            "createdAt": postgres_supplier.get('created_at'),
            "lastUpdatedAt": postgres_supplier.get('updated_at'),
            "totalCapacity": 0.0,  # Default value
            "revenue": 0.0,  # Default value
            "paymentTerms": {
                "term": "",
                "days": 0
            },
            "gstin": postgres_supplier.get('gstin'),
            "pan": postgres_supplier.get('pan_id'),
            "contactPerson": {
                "firstName": primary_contact.get('first_name', ''),
                "lastName": primary_contact.get('last_name', ''),
                "email": primary_contact.get('email', ''),
                "phone": primary_contact.get('phone', '')
            }
        }

    def sync_suppliers(self) -> bool:
        """Sync suppliers from ProcureStack to MongoDB"""
        try:
            # Fetch suppliers modified since last sync
            select_query = """
                SELECT *
                FROM suppliers
                WHERE updated_at >= NOW() - INTERVAL '5 minutes'
                    OR created_at >= NOW() - INTERVAL '5 minutes'
            """
            
            suppliers = self.procure_db.read_data(select_query, {"execution_time": QueryRun})
            
            if not suppliers:
                logging.info("No suppliers found to sync")
                return True
                
            logging.info(f"Found {len(suppliers)} suppliers to sync")
            
            success_count = 0
            for supplier in suppliers:
                try:
                    # Transform supplier to MongoDB format
                    mongo_supplier = self.transform_postgres_to_mongo_supplier(supplier)
                    
                    # Check if supplier exists
                    existing_supplier = self.mongo_ops.read_data(
                        collection_name="supplier",
                        query={"supplierId": mongo_supplier["supplierId"]}
                    ) if mongo_supplier["supplierId"] else []

                    if existing_supplier:
                        # Update existing supplier
                        result = self.mongo_ops.update_data(
                            collection_name="supplier",
                            query={"supplierId": mongo_supplier["supplierId"]},
                            update={"$set": mongo_supplier}
                        )
                        logging.info(f"Updated existing supplier: {mongo_supplier['supplierId']}")
                    else:
                        # Generate new supplier ID
                        new_supplier_id = self.generate_next_supplier_id()
                        mongo_supplier["supplierId"] = new_supplier_id
                        
                        # Insert new supplier
                        result = self.mongo_ops.write_data(
                            collection_name="supplier",
                            data=mongo_supplier
                        )
                        
                        # Convert UUID to string before using in update query
                        supplier_id = str(supplier["id"]) if isinstance(supplier["id"], uuid.UUID) else supplier["id"]
                        
                        # Update PostgreSQL with the new supplier_id
                        update_query = """
                            UPDATE suppliers 
                            SET supplier_id = :supplier_id 
                            WHERE id = :id
                        """
                        self.procure_db.update_data(
                            update_query, 
                            {
                                "supplier_id": new_supplier_id,
                                "id": supplier_id
                            }
                        )
                        
                        logging.info(f"Created new supplier with ID: {new_supplier_id}")
                    
                    success_count += 1
                    
                except Exception as e:
                    logging.error(f"Error syncing supplier {supplier.get('id')}: {str(e)}")
                    continue
            
            logging.info(f"Successfully synced {success_count}/{len(suppliers)} suppliers")
            return True
            
        except Exception as e:
            logging.error(f"Error in supplier sync: {str(e)}")
            raise

def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""
    def wrapper():
        sync_manager = SupplierSyncManager()
        try:
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")
        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            raise
    return wrapper

@create_sync_task
def sync_suppliers(manager: SupplierSyncManager) -> bool:
    """Sync suppliers from ProcureStack to MongoDB"""
    return manager.sync_suppliers()

# Create the DAG
dag = DAG(
    'procure_to_mongo_suppliers_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
    },
    description='Sync suppliers from ProcureStack to MongoDB',
    schedule='*/5 * * * *',
     # or alternatively: schedule=timedelta(minutes=20),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'mongodb', 'postgres', 'suppliers'],
    dagrun_timeout=timedelta(minutes=10),
    max_active_runs=1,
)

# Create task
sync_task = PythonOperator(
    task_id='sync_suppliers',
    python_callable=sync_suppliers,
    dag=dag,
    execution_timeout=timedelta(minutes=5),
)
