<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action Required - {{ stack_name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fdfdfd;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            color: #333;
        }
        .alert {
            background-color: #fff3cd;
            padding: 15px 20px;
            border-left: 5px solid #ffc107;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .urgent h3 {
            margin-top: 0;
            color: #dc3545;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: #fff;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-TODO {
            color: #ffc107;
            font-weight: bold;
        }
        .status-IN_PROGRESS {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-COMPLETED {
            color: #28a745;
            font-weight: bold;
        }
        .status-BLOCKED {
            color: #dc3545;
            font-weight: bold;
        }
        .group-tag {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .action-link {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Action Required - {{ stack_name }}</h2>
    </div>
    <p>Dear {{ sales_team_member }},</p>
    <p>The following task in <strong>{{ stack_name }}</strong> requires your attention. Please take the required action as soon as possible.</p>
    <div class="delayed-tasks">
        <table>
            <thead>
                <tr>
                    <th>Action Required</th>
                    <th>Enquiry/PO Number</th>
                    <th>Customer Name</th>
                    <th>State</th>
                    <th>Due Date & Time</th>
                </tr>
            </thead>
            <tbody>
                {% for task in tasks %}
                <tr>
                    <td><a href="{{ task.action_link }}" class="action-link">{{ task.action_required }}</a></td>
                    <td>{{ task.enquiry_id }}</td>
                    <td>{{ task.customer_name }}</td>
                    <td>{{ task.current_status }}</td>
                    <td>{{ task.due_date }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <p>Thank you for your attention to this matter.</p>

    <p>Best Regards,<br>Team Mstack</p>
</body>
</html>