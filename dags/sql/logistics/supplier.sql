 CREATE TABLE IF NOT EXISTS logistics.supplier(
    supplier_uid VARCHAR(255),
    supplier_id VARCHAR(255),
    supplier_name VARCHAR(255),
    email VARCHAR(255),
    mobile VARCHAR(255),
    deleted BOOLEAN,
    created_by VA<PERSON>HA<PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    total_capacity FLOAT,
    revenue FLOAT,
    street VARCHAR(255),
    state VARCHAR(255),
    country VARCHAR(255),
    postal_code VARCHAR(255),
    city VARCHAR(255),

    PRIMARY KEY(supplier_uid)
);


 CREATE TABLE IF NOT EXISTS logistics.supplier_products(
    supplier_product_uid VARCHAR(255),
    capacity_available FLOAT,
    total_capacity FLOAT,
    lead_time INTEGER,
    typical_order_size FLOAT,
    hazardous BOOLEAN,
    product_id VARCHAR(255),
    street VARCHAR(255),
    city VARCHAR(255),
    postal_code VARCHAR(255),
    country VARCHAR(255),
    hs_code VARCHAR(255),
    state VARCHAR(255),
    supplier_id VARCHAR(255),

    PRIMARY KEY(supplier_product_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid)
);