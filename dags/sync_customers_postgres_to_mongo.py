from datetime import datetime, timedelta, timezone
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from airflow import DAG
from airflow.operators.python import PythonOperator
import logging
from typing import List, Dict, Any
from config.db_config import DatabaseConfig
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.default import QueryRun

def transform_postgres_to_mongo_customer(postgres_customer: Dict[str, Any]) -> Dict[str, Any]:
    """Transform PostgreSQL customer data to MongoDB format"""
    return {
        "customerId": postgres_customer["customer_id"],
        "name": postgres_customer["customer_full_name"],
        "email": postgres_customer["customer_email"],
        "address": {
            "addressLine1": postgres_customer.get("address", ""),
            "city": postgres_customer.get("city", ""),
            "country": postgres_customer.get("country", ""),
            "postalCode": postgres_customer.get("postal_code", "")
        },
        "mobile": postgres_customer.get("customer_phone", ""),
        "companySize": postgres_customer.get("size", ""),
        "accountOwner": postgres_customer.get("account_owner", "SYSTEM"),
        "type": postgres_customer.get("type", "END_CUSTOMER"),  # Default value
        "categories": [],  # Default empty list
        "remarks": [],  # Default empty list
        "l1Reviewers": [],  # Default empty list
        "l2Reviewers": [],  # Default empty list
        "deleted": False,
        "createdBy": postgres_customer.get("created_by", "SYSTEM"),
        "lastUpdatedBy": postgres_customer.get("modified_by", "SYSTEM"),
        "createdAt": postgres_customer.get("created_at", datetime.now(timezone.utc)),
        "lastUpdatedAt": postgres_customer.get("modified_at", datetime.now(timezone.utc)),
        "documents": {}
    }

def generate_next_customer_id(mongo_ops: MongoOperations) -> str:
    """Generate next customer ID in format CS00001"""
    # # Get all customers and find the highest number
    # all_customers = mongo_ops.read_data(
    #     collection_name="customer",
    #     query={},
    #     projection={"customerId": 1}
    # )
    
    # max_num = 0
    # for customer in all_customers:
    #     customer_id = customer.get('customerId', '')
    #     if customer_id and customer_id.startswith('CS'):
    #         try:
    #             num = int(customer_id[2:])
    #             max_num = max(max_num, num)
    #         except ValueError:
    #             continue
    
    # next_num = max_num + 1
    # return f"CS{next_num:05d}"  # Format as CS00001, CS00002, etc.
    result = mongo_ops.get_collection("customer").aggregate([
        {
            "$match": {
                "customerId": {"$regex": "^CS\\d+$"}  # Match CS followed by numbers
            }
        },
        {
            "$project": {
                "num": {"$toInt": {"$substr": ["$customerId", 2, -1]}}
            }
        },
        {
            "$group": {
                "_id": None,
                "maxNum": {"$max": "$num"}
            }
        }
    ])
    
    max_num = next(result, {"maxNum": 0})["maxNum"]
    return f"CS{max_num + 1:05d}"

def sync_customers():
    """Sync customers from PostgreSQL to MongoDB"""
    try:
        # Initialize connections
        postgres_params = DatabaseConfig.get_sales_stack_postgres_params()
        mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
        
        postgres_ops = PostgresOperations(postgres_params)
        mongo_ops = MongoOperations(mongo_conn_string, mongo_db)
        
        try:
            # Get customers updated since last sync
            select_query = """
                SELECT 
                    c.id,
                    c.customer_id,
                    c.customer_full_name,
                    c.customer_email,
                    c.customer_phone,
                    c.customer_company,
                    c.address,
                    c.city,
                    c.country,
                    c.postal_code,
                    c.account_owner,
                    c.created_at AT TIME ZONE 'UTC' as created_at,
                    c.modified_at AT TIME ZONE 'UTC' as modified_at,
                    c.created_by,
                    c.modified_by,
                    c.size,
                    c.type
                FROM public.customer c
                WHERE c.modified_at >= NOW() - INTERVAL '5 minutes'
                    OR c.created_at >= NOW() - INTERVAL '5 minutes'
            """
            
            logging.info(f"Executing query with execution_time: {QueryRun}")
            customers = postgres_ops.read_data(select_query, {"execution_time": QueryRun})
            
            if not customers:
                logging.info("No customers found to sync")
                return True
                
            logging.info(f"Found {len(customers)} customers to sync")
            logging.info(f"Customers: {customers}")
            
            success_count = 0
            for postgres_customer in customers:
                try:
                    # Transform PostgreSQL customer to MongoDB format
                    mongo_customer = transform_postgres_to_mongo_customer(postgres_customer)
                    
                    # Debug log
                    logging.info(f"Processing customer with ID: {postgres_customer['customer_id']}")
                    logging.info(f"Modified at: {postgres_customer['modified_at']}")
                    
                    # Check if customer exists in MongoDB
                    existing_customers = mongo_ops.read_data(
                        collection_name="customer",
                        query={"customerId": mongo_customer["customerId"]}
                    )
                    
                    logging.info(f" existing customer: {existing_customers}")
                    if existing_customers:
                        # Update existing customer
                        result = mongo_ops.update_data(
                            collection_name="customer",
                            query={"customerId": mongo_customer["customerId"]},
                            update={"$set": mongo_customer}
                        )
                        logging.info(f"Updated existing customer: {result}")
                    else:
                        # Generate new customer ID
                        new_customer_id = generate_next_customer_id(mongo_ops)
                        mongo_customer["customerId"] = new_customer_id
                        
                        # Insert new customer in MongoDB
                        result = mongo_ops.write_data(
                            collection_name="customer",
                            data=mongo_customer
                        )
                        
                        # Update PostgreSQL with the new customer_id
                        update_query = """
                            UPDATE public.customer 
                            SET customer_id = :customer_id 
                            WHERE id = :id
                        """
                        postgres_ops.update_data(
                            update_query, 
                            {
                                "customer_id": new_customer_id,
                                "id": postgres_customer["id"]
                            }
                        )
                        
                        logging.info(f"Created new customer with ID: {new_customer_id}")
                    
                    success_count += 1
                    
                except Exception as e:
                    logging.error(f"Error syncing customer {postgres_customer.get('id')}: {str(e)}")
                    continue
            
            logging.info(f"Successfully synced {success_count}/{len(customers)} customers")
            return True
            
        finally:
            postgres_ops.close()
            mongo_ops.close()
            logging.info("Database connections closed")
            
    except Exception as e:
        logging.error(f"Error in customer sync: {str(e)}")
        raise

# Create the DAG
dag = DAG(
    'sync_customers_postgres_to_mongo',
    default_args={
        'owner': 'airflow',
        'depends_on_past': False,
        'email_on_failure': False,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5),
    },
    description='Sync customers from PostgreSQL to MongoDB',
    schedule='*/5 * * * *',  # Run every 5 minutes
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    catchup=False,
    tags=['sync', 'mongodb', 'postgresql', 'customers'],
)

# Create sync task
sync_customers_task = PythonOperator(
    task_id='sync_customers',
    python_callable=sync_customers,
    dag=dag,
)
