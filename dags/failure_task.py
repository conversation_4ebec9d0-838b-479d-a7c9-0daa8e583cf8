# failure_callback.py

import traceback
import requests
import logging
from airflow.models import TaskInstance
from airflow.utils.email import send_email
from airflow.models import Variable
from ses_hook import SESHook
from config.db_config import DatabaseConfig

def send_slack_alert(context):
    slack_webhook_url = Variable.get("slack_webhook_url")  # Set this in Airflow Variables
    env_type = Variable.get("mstack_deployment_env")
    slack_message = f"""
    :red_circle: *{env_type} Airflow Alert* :warning:
    
    :boom: *Task Failure Details* :boom:
    -------------------------------------
    :arrow_right: *DAG:* `{context.get('dag').dag_id}`
    :arrow_right: *Task:* `{context.get('task_instance').task_id}`
    :arrow_right: *Execution Time:* `{context.get('execution_date')}`
    
    :link: *Log URL:* 
    {context.get('task_instance').log_url}
    
    :x: *Exception Stack Trace:*
    ```{str(traceback.format_exc())}```
    """
    slack_response = requests.post(
            slack_webhook_url,
            json={"text": slack_message}
        )
    if slack_response.status_code != 200:
        logging.warning(f"Request to Slack returned error {slack_response.status_code}, response: {slack_response.text}")
    else:
        logging.info("Failure alert sent to Slack successfully.")