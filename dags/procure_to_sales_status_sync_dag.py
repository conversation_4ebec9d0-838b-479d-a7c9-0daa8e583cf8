import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager
from airflow import DAG
from airflow.operators.python import PythonOperator
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun
from config.status_mapping import create_status_mapping
from db.supabase_operations import SupabaseOperations
from config.supabase_config import SupabaseConfig
from airflow.models.baseoperator import chain

from ses_hook import SESHook
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert

# Constants
BATCH_SIZE = 100
MAX_RETRIES = 0
RETRY_DELAY = timedelta(minutes=5)

# Get status mapping
STATUS_MAPPING = create_status_mapping()
PROCURE_TO_SALES_MAPPING = STATUS_MAPPING['ProcureStack_to_SalesStack']
PROCURE_TO_SALES_SAMPLE_MAPPING = STATUS_MAPPING['Sample_Procure_to_Sales']


class EnquiryStatusSyncManager:
    """Manager class for enquiry status synchronization"""

    def __init__(self):
        self.procure_db: Optional[PostgresOperations] = None
        self.sales_db: Optional[PostgresOperations] = None
        self.procure_supabase: Optional[SupabaseOperations] = None
        self.sales_supabase: Optional[SupabaseOperations] = None
        self.procure_quotation_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['PROCURE_STACK']['QUOTATION_DOCUMENTS']
        self.sales_quotation_documents_bucket = SupabaseConfig.STORAGE_BUCKETS['SALES_STACK']['QUOTATION_DOCUMENTS']
        self.status_mapping = create_status_mapping()['ProcureStack_to_SalesStack']
        self.valid_transitions = list(self.status_mapping.keys())
        #self.valid_enquiry_status_transitions = list(self.status_mapping.values())
        logging.info(PROCURE_TO_SALES_MAPPING)

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            sales_params = DatabaseConfig.get_sales_stack_postgres_params()

            self.procure_db = PostgresOperations(procure_params)
            self.sales_db = PostgresOperations(sales_params)

            # Initialize Supabase connections
            procure_supabase_params = SupabaseConfig.get_procure_stack_supabase_params()
            sales_supabase_params = SupabaseConfig.get_sales_stack_supabase_params()

            # Pass bucket names to SupabaseOperations
            procure_supabase_params['bucket_name'] = self.procure_quotation_documents_bucket
            sales_supabase_params['bucket_name'] = self.sales_quotation_documents_bucket

            self.procure_supabase = SupabaseOperations(procure_supabase_params)
            self.sales_supabase = SupabaseOperations(sales_supabase_params)

            logging.info(f"Connected to databases - Procure: {procure_params['host']}, Sales: {sales_params['host']}")
            yield

        finally:
            if self.procure_db:
                self.procure_db.close()
            if self.sales_db:
                self.sales_db.close()
            logging.info("Database connections closed")

    def sync_table_data(self, select_query: str, select_query_params: Dict[str, Any], insert_or_update_query: str,
                        use_case: str, **kwargs) -> Optional[bool]:
        status_changes = self.procure_db.read_data(select_query, select_query_params)

        if not status_changes:
            logging.info("No status changes found to sync")
            return True

        logging.info(f" {use_case} :  {len(status_changes)} records found to sync")

        success_count = 0

        for record in status_changes:
            try:
                # Convert RowMapping to dictionary
                record_dict = dict(record)

                if use_case == 'main_enquiry_status':
                    mapping_entry = PROCURE_TO_SALES_MAPPING.get(record_dict['current_status'])
                    logging.info(f"Mapped status entry: '{mapping_entry}' for record  {record_dict['id']}") # Log the entire entry
                    if not mapping_entry:
                        logging.warning(f"No mapping found for status '{record_dict['current_status']}' in PROCURE_TO_SALES_MAPPING. Skipping record {record_dict['id']}")
                        continue

                    sales_status = mapping_entry.get('SalesStack')
                    previous_valid_states = mapping_entry.get('PreviousValidStates')

                    if not sales_status:
                        logging.warning(f"No 'SalesStack' status defined for Procure status '{record_dict['current_status']}' in mapping. Skipping record {record_dict['id']}")
                        continue

                    if sales_status == 'clarification_needed' and self.clarification_back_update_check(record_dict['id']):
                        logging.warning(
                            f"No 'clarification needed update skipped because clarification already resolved in sales")
                        continue

                    record_dict['current_status'] = sales_status
                    record_dict['previous_valid_states'] = previous_valid_states if previous_valid_states is not None else []



                elif use_case == 'enquiry_status':
                    # Map the 'status' field for status history
                    mapping_entry = PROCURE_TO_SALES_MAPPING.get(record_dict['status'])
                    logging.info(f"for enquiry {record_dict['enquiry_id']} Mapped status entry: {mapping_entry} and status {record_dict['status']}")

                    if not mapping_entry:
                        logging.warning(f"No mapping found for status '{record_dict['status']}' in PROCURE_TO_SALES_MAPPING for status history. Skipping record {record_dict['id']}")
                        continue

                    sales_status = mapping_entry.get('SalesStack')
                    if not sales_status:
                         logging.warning(f"No 'SalesStack' status defined for Procure status '{record_dict['status']}' in mapping for status history. Skipping record {record_dict['id']}")
                         continue

                    record_dict['status'] = sales_status # Update the status to the mapped SalesStack status

                elif use_case == 'sample_status_history':
                    mapping_entry = PROCURE_TO_SALES_SAMPLE_MAPPING.get(record_dict['sample_status'])

                    logging.info(
                        f"for Sample {record_dict['sample_request_id']} Mapped status entry: {mapping_entry} and status {record_dict['sample_status']}")

                    if not mapping_entry:
                        logging.warning(
                            f"No mapping found for status '{record_dict['sample_status']}' in PROCURE_TO_SALES_MAPPING for status history. Skipping record {record_dict['id']}")
                        continue

                    sales_status = mapping_entry.get('SalesStack')
                    if not sales_status:
                        logging.warning(
                            f"No 'SalesStack' status defined for Procure status '{record_dict['sample_status']}' in mapping for status history. Skipping record {record_dict['id']}")
                        continue

                    record_dict['sample_status'] = sales_status  # Update the status to the mapped SalesStack status

                elif use_case == 'sample_requests':

                    mapping_entry = PROCURE_TO_SALES_SAMPLE_MAPPING.get(record_dict['status'])
                    logging.info(
                        f"Mapped status entry: '{mapping_entry}' for record  {record_dict['id']}")  # Log the entire entry
                    if not mapping_entry:
                        logging.warning(
                            f"No mapping found for status '{record_dict['status']}' in PROCURE_TO_SALES_MAPPING. Skipping record {record_dict['id']}")
                        continue

                    sales_status = mapping_entry.get('SalesStack')
                    previous_valid_states = mapping_entry.get('PreviousValidStates')

                    if not sales_status:
                        logging.warning(
                            f"No 'SalesStack' status defined for Procure status '{record_dict['status']}' in mapping. Skipping record {record_dict['id']}")
                        continue

                    record_dict['status'] = sales_status
                    record_dict['previous_valid_states'] = previous_valid_states if previous_valid_states is not None else []

                elif use_case == 'quote_generation_attachments':
                    file_path = record_dict['file_path'].strip()

                    # Check if file exists in sales bucket
                    if not self.sales_supabase.check_file_exists(file_path):
                        # Transfer file between buckets if it doesn't exist
                        if not self.procure_supabase.transfer_file(file_path, self.sales_supabase):
                            continue # Skip write if transfer fails

                self.sales_db.write_data(insert_or_update_query, record_dict)
                success_count += 1
                logging.info(f"Successfully synced {success_count}/{len(status_changes)} status changes")
            except Exception as e:
                # Ensure record_dict exists for logging, provide a default if not
                record_id = record.get('id', 'unknown_id') if 'record_dict' in locals() else 'unknown_id'
                logging.error(f"Error syncing record ID {record_id} for use case '{use_case}'. Error: {str(e)}")
                send_exception_slack_alert(e, context_info=kwargs)
                # Continue to the next record
                continue

        logging.info(f"Successfully synced {success_count}/{len(status_changes)} details")


    def clarification_back_update_check(self, enquiry_id, **kwargs) -> bool:
        try:
            select_query = f"""select id from enquiry_clarifications ec where ec.enquiry_id =:enquiry_id and status ='pending'"""

            select_query_params = { "enquiry_id": enquiry_id }

            data = self.sales_db.read_data(select_query, select_query_params)
            logging.info(f"Sales Clarification data: {data} , {enquiry_id}")
            if data:
                return False
            return True
        except Exception as e:
            logging.error(f"Error updating status_changes table: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            raise



    def update_status_changes_synced(self, status_id: str, **kwargs) -> bool:

        try:
            update_query = f"""UPDATE status_changes
                                    SET is_synced = true
                                    WHERE id = :status_id
                                """

            update_params = {
                "status_id": status_id
            }

            self.procure_db.write_data(update_query, update_params)
            return True
        except Exception as e:
            logging.error(f"Error updating status_changes table: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            
            raise

    def update_sales_enquiry_status(self, **kwargs) -> bool:
        """Update enquiry status in sales database"""
        try:

            current_status_query = f"""
                SELECT id, current_status, last_status_change
                FROM enquiries
                WHERE last_status_change >= :execution_time
            """

            update_query = f"""
                UPDATE enquiries
                SET current_status = :current_status,
                    last_status_change = :last_status_change
                WHERE id = :id
                AND current_status != :current_status
                AND ( -- Check if the CURRENT Sales status allows transition TO the target status
                    array_length(:previous_valid_states, 1) IS NULL -- Allow if no previous states defined
                    OR current_status::text = ANY(:previous_valid_states) -- Allow if current state is in the list
                )
            """

            self.sync_table_data(current_status_query, {"execution_time": QueryRun}
                                 , update_query, 'main_enquiry_status')

            return True

        except Exception as e:
            logging.error(f"Error updating enquiry status in sales DB: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def sync_status_history_tables(self, **kwargs) -> bool:
        """Sync status changes to enquiry_status_history"""
        try:
            # Get data from procure status_changes table
            select_query = """
                SELECT
                    stc.id,
                    stc.enquiry_id,
                    stc.new_state as status,
                    stc.notes,
                    stc.created_by,
                    stc.created_at,
                    enq.sales_team_member  as  sales_agent_email,
                    enq.procurement_poc,
                    stc.is_synced
                FROM status_changes stc
                INNER JOIN enquiries enq ON enq.id = stc.enquiry_id
                WHERE stc.created_at >= :execution_time  and stc.new_state = ANY(:procure_statuses)
                ORDER BY stc.created_at
            """

            insert_query = """
                    INSERT INTO public.enquiry_status_history (
                        id,
                        enquiry_id,
                        status,
                        notes,
                        created_at,
                        changed_by,
                        sales_agent_email,
                        procurement_poc
                    )
                    VALUES (
                    :id, :enquiry_id, :status, :notes, :created_at, :created_by,
                    :sales_agent_email, :procurement_poc
                    )
                    ON CONFLICT (id) DO NOTHING
                    """


            # Clone the list and remove 'suppliers_identified' for this specific sync
            statuses_for_history = list(self.valid_transitions)
            if 'suppliers_identified' in statuses_for_history:
                statuses_for_history.remove('suppliers_identified')

            self.sync_table_data(select_query, {"procure_statuses": statuses_for_history, "execution_time": QueryRun}, insert_query,
                                 'enquiry_status')

            return True

        except Exception as e:
            logging.error(f"Error syncing status history: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def sync_all_pending_clarifications(self, **kwargs) -> bool:
        """Sync all pending clarification records from procure to sales database"""
        try:
            # Get all pending clarification records from procure database
            select_query = """
                SELECT
                    id,
                    enquiry_id,
                    query,
                    response,
                    created_at,
                    resolved_at,
                    created_by,
                    status
                FROM public.enquiry_clarifications
                WHERE status = 'pending'
            """

            # Insert into sales database
            insert_query = """
                INSERT INTO public.enquiry_clarifications (
                    id, enquiry_id, query, response, created_at,
                    resolved_at, created_by, status
                )
                VALUES (
                    :id, :enquiry_id, :query, :response, :created_at,
                    :resolved_at, :created_by, :status
                )
                ON CONFLICT (id) DO NOTHING
            """

            self.sync_table_data(select_query, {}, insert_query, 'clarification_records')

            return True


        except Exception as e:
            logging.error(f"Error syncing clarification records: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def sync_quote_generation_details(self, **kwargs) -> bool:
        """Sync quote generation details from procure to sales database"""
        try:
            # Get quote generation details from procure database
            select_query = """
                SELECT
                    id, enquiry_id, generated_by,
                    generated_at, pdf_file_path, notes, status_change_id as status_history_id
                    , inco_terms, payment_terms
                FROM quote_generation_details
                WHERE generated_at >= :execution_time
            """

            insert_query = """
                INSERT INTO quote_generation_details(
                    id, enquiry_id, generated_by,
                    generated_at, pdf_file_path, notes, status_history_id
                    , inco_terms, payment_terms
                )
                VALUES (
                    :id, :enquiry_id, :generated_by,
                    :generated_at, :pdf_file_path, :notes, :status_history_id
                    , :inco_terms, :payment_terms
                )
                ON CONFLICT (id) DO NOTHING
            """

            self.sync_table_data(select_query, {"execution_time": QueryRun}, insert_query, 'quote_generation_details')

            # Get quote generation details from procure database
            select_query = """
                            SELECT id, quote_generation_id, customer_name, product_name,
                                      packaging_name, price, currency, quantity, unit, amount,
                                      po_to_delivery_time, expiry_date, created_at, updated_at, cas
                            FROM quote_generation_options
                            WHERE created_at >= :execution_time
                        """

            insert_query = """
                            insert into quote_generation_options (id, quote_generation_id, customer_name, product_name,
                                      packaging_name, price, currency, quantity, unit, amount,
                                      po_to_delivery_time, expiry_date, created_at, updated_at, cas)
                            values (:id, :quote_generation_id, :customer_name, :product_name, :packaging_name, :price,
                                    :currency, :quantity, :unit, :amount, :po_to_delivery_time, :expiry_date,
                                    :created_at, :updated_at, :cas)

                                                        ON CONFLICT (id) DO NOTHING
                            """

            self.sync_table_data(select_query, {"execution_time": QueryRun}, insert_query, 'quote_generation_options')

            select_document_query = """select id, quote_generation_id, file_name, file_path, content_type, size, created_at from
                                                   public.quote_generation_attachments where created_at >= :execution_time """

            insert_document_query = """insert into quote_generation_attachments (id, quote_generation_id, file_name, file_path, content_type, size, created_at)
                                       values (:id, :quote_generation_id, :file_name, :file_path, :content_type, :size, :created_at)
                                       on conflict (id) do nothing
                   """
            self.sync_table_data(select_document_query, {"execution_time": QueryRun}, insert_document_query,
                                 'quote_generation_attachments')
            return True

        except Exception as e:
            logging.error(f"Error syncing quote generation details: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

    def sync_sample_status_processing_details(self, **kwargs) -> bool:

        try:

            sample_status_mapping = create_status_mapping()['Sample_Procure_to_Sales']
            sample_valid_transitions = list(sample_status_mapping.keys())

            select_query = """
                           select id, sample_request_id, sample_status, changed_at, enquiry_id from sample_status_history
                           where changed_at >= :execution_time and sample_status = ANY(:sample_status)
                           """

            insert_query = """
                           INSERT INTO sample_status_history (id, sample_request_id, sample_status, changed_at, enquiry_id)
                           VALUES (:id, :sample_request_id, :sample_status, :changed_at, :enquiry_id) 
                               ON CONFLICT (sample_request_id, sample_status) DO NOTHING
                           """

            self.sync_table_data(select_query,
                                 {"sample_status": sample_valid_transitions, "execution_time": QueryRun},
                                 insert_query,
                                 'sample_status_history')

            return True

        except Exception as e:
            logging.error(f"Error syncing sample processing details for enquiry: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False


    def sync_sample_request_processing_details(self, **kwargs) -> bool:

        try:
            select_query = """
                           select id, status, mrd_date, expected_delivery_date, tracking_number, carrier_name, tracking_url 
                           from sample_requests where last_status_change >= :execution_time
                           """

            update_query = """
                           update sample_requests set status = :status,
                                                      expected_delivery_date =:expected_delivery_date,
                                                      tracking_number =:tracking_number,
                                                      carrier_name = :carrier_name,
                                                      tracking_url = :tracking_url
                               where id = :id and status::text = ANY(:previous_valid_states)
                           """

            self.sync_table_data(select_query, {"execution_time": QueryRun},
                                 update_query,
                                 'sample_requests')

            return True

        except Exception as e:
            logging.error(f"Error syncing sample processing details for enquiry: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            return False

def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""

    def wrapper(*args, **kwargs):
        sync_manager = EnquiryStatusSyncManager()
        try:
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")
        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            raise

    return wrapper


@create_sync_task
def sync_enquiry_status(manager: EnquiryStatusSyncManager) -> bool:
    return manager.update_sales_enquiry_status()


@create_sync_task
def sync_clarification_records(manager: EnquiryStatusSyncManager) -> bool:
    return manager.sync_all_pending_clarifications()


@create_sync_task
def sync_status_history(manager: EnquiryStatusSyncManager) -> bool:
    return manager.sync_status_history_tables()


@create_sync_task
def sync_quote_generation_details(manager: EnquiryStatusSyncManager) -> bool:
    return manager.sync_quote_generation_details()

@create_sync_task
def sync_sample_status_details(manager: EnquiryStatusSyncManager) -> bool:
    return manager.sync_sample_status_processing_details()


@create_sync_task
def sync_sample_request_details(manager: EnquiryStatusSyncManager) -> bool:
    return manager.sync_sample_request_processing_details()



# Create the DAG
dag = DAG(
    'procure_to_sales_all_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
        'on_callback_failure': send_slack_alert
        #'retries': MAX_RETRIES,
        #'retry_delay': RETRY_DELAY,
    },
    description='Sync enquiry status and clarification records from ProcureStack to SalesStack',
    schedule=timedelta(minutes=3),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'postgres', 'enquiries'],
    dagrun_timeout=timedelta(minutes=10),  # Auto-fail after 10 minutes
    max_active_runs=1,  # No concurrent runs
)

# Create tasks with consistent configuration
tasks = []
for task_id, task_func in [
    ('sync_enquiry_status', sync_enquiry_status),
    ('sync_clarification_records', sync_clarification_records),
    ('sync_status_history', sync_status_history),
    ('quote_generation_details', sync_quote_generation_details),
    ('sync_sample_status_details', sync_sample_status_details),
    ('sync_sample_request_details', sync_sample_request_details),
]:
    tasks.append(PythonOperator(
        task_id=task_id,
        python_callable=task_func,
        dag=dag,
        execution_timeout=timedelta(minutes=5),
    ))

# Set task dependencies using chain
chain(*tasks)
