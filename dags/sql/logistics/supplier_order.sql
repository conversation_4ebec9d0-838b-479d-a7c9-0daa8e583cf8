 CREATE TABLE IF NOT EXISTS logistics.supplier_order(
    supplier_order_uid VARCHAR(255),
    order_id VARCHAR(255),
    purchase_order_number VARCHAR(255),
    purchase_order_date TIMESTAMP,
    inco_type VARCHAR(255),
    inco_country VARCHAR(255),
    inco_shipment_method VARCHAR(255),
    inco_port VARCHAR(255),
    status VARCHAR(255),
    created_at TIMESTAMP,
    created_by VARCHAR(255),
    per_unit_weight FLOAT,
    approved BOOLEAN,
    deleted BOOLEAN,
    linked_supplier_order_book_id VARCHAR(255),
    updated_at TIMESTAMP,
    updated_by VARCHAR(255),
    supplier_id VARCHAR(255),
    actual_date_of_departure TIMESTAMP,

    PRIMARY KEY(supplier_order_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid),
    FOREIGN KEY(linked_supplier_order_book_id) REFERENCES logistics.supplier_order_book(supplier_order_book_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.supplier_order_products(
    supplier_id VARCHAR(255),
    supplier_order_product_uid VARCHAR(255),
    supplier_order_uid VARCHAR(255),
    supplier_product_uid VARCHAR(255),
    unit_of_measure VARCHAR(255),
    quantity INTEGER,
    price FLOAT,
    hs_code VARCHAR(255),
    units INTEGER,
    status VARCHAR(255),
    quantity_per_unit FLOAT,
    per_unit_weight FLOAT,
    product_id VARCHAR(255),
    packaging_id VARCHAR(255),
    chemstack_price FLOAT,
    per_unit_kg_value FLOAT,

    PRIMARY KEY(supplier_order_product_uid),
    FOREIGN KEY(supplier_order_uid) REFERENCES logistics.supplier_order(supplier_order_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.supplier_order_supplier_products(
    supplier_order_uid VARCHAR(255),
    supplier_id VARCHAR(255),
    capacity_available FLOAT,
    total_capacity FLOAT,
    lead_time INTEGER,
    typical_order_size FLOAT,
    hazardous BOOLEAN,
    product_id VARCHAR(255),
    street VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    postal_code VARCHAR(255),
    country VARCHAR(255),
    hs_code VARCHAR(255),
    revenue FLOAT,

    PRIMARY KEY(product_id, supplier_id),
    FOREIGN KEY(supplier_order_uid) REFERENCES logistics.supplier_order(supplier_order_uid),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.supplier_order_product_packaging(
    product_id VARCHAR(255),
    supplier_id VARCHAR(255),
    packaging_id VARCHAR(255),

    PRIMARY KEY(product_id, supplier_id),
    FOREIGN KEY(supplier_id) REFERENCES logistics.supplier(supplier_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid)
    
    
    );