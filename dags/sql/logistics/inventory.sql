
CREATE TABLE IF NOT EXISTS logistics.inventory_product(
    inventory_product_uid VARCHAR(255),
    inventory_id VARCHAR(255),
    product_id VARCHAR(255),
    product_name VARCHAR(255),
    packaging_id VARCHAR(255),
    units INTEGER,
    deleted BO<PERSON>EAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),

    PRIMARY KEY(inventory_product_uid),
    FOREIGN KEY(product_id) REFERENCES logistics.product(product_uid),
    FOREIGN KEY(packaging_id) REFERENCES logistics.packaging(packaging_uid)
);

 CREATE TABLE IF NOT EXISTS logistics.inventory_product_transaction(
    inventory_product_transaction_uid VARCHAR(255),
    transaction_id VARCHAR(255),
    inventory_product_id VARCHAR(255),
    units FLOAT,
    transaction_type VARCHAR(255),
    created_at TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ordered_product_id VARCHAR(255),
    order_book_id VARCHAR(255),
    purchase_order_number VARCHAR(255),
    supplier_order_book_id VARCHAR(255),
    updated_at TIMESTAMP,
    updated_by VARC<PERSON>R(255),

    PR<PERSON>ARY KEY(inventory_product_transaction_uid),
    FOREIGN KEY(inventory_product_id) REFERENCES logistics.inventory_product(inventory_product_uid)
   -- FOREIGN KEY(ordered_product_id) REFERENCES logistics.order_book_products(order_book_product_id),
    --FOREIGN KEY(order_book_id) REFERENCES logistics.order_book(order_book_uid),
    --FOREIGN KEY(supplier_order_book_id) REFERENCES logistics.supplier_order_book(supplier_order_book_uid)
);