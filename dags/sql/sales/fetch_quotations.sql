 WITH latest_quotations AS (
            SELECT 
                enquiry_id,
                MAX(created_at) as latest_created_at
            FROM 
                public.enquiry_status_history
            WHERE 
                status = 'pricing_quotation_generated'
                AND created_at > :last_run_time
            GROUP BY 
                enquiry_id
        ),
        latest_status AS (
            -- Get the latest status for each enquiry regardless of what it is
            SELECT 
                enquiry_id,
                MAX(created_at) as latest_status_date
            FROM 
                public.enquiry_status_history
            GROUP BY 
                enquiry_id
        )
        SELECT 
            esh.id,
            esh.enquiry_id,
            esh.status,
            esh.notes,
            esh.created_at,
            esh.changed_by,
            esh.sales_agent_email,
            e.chemical_name,
            e.product,
            e.cas_number,
            c.customer_full_name,
            c.customer_company,
            c.customer_email,
            e.customer_id
        FROM 
            public.enquiry_status_history esh
        JOIN 
            latest_quotations lq 
            ON esh.enquiry_id = lq.enquiry_id 
            AND esh.created_at = lq.latest_created_at
        JOIN 
            latest_status ls
            ON esh.enquiry_id = ls.enquiry_id
            AND esh.created_at = ls.latest_status_date
        JOIN 
            public.enquiries e 
            ON esh.enquiry_id = e.id
        JOIN 
            public.customer c 
            ON e.customer_id = c.id
        WHERE 
            esh.status = 'pricing_quotation_generated'
            AND esh.created_at > :max_stale_threshold
            AND esh.created_at < :stale_threshold            
        ORDER BY 
            esh.created_at DESC