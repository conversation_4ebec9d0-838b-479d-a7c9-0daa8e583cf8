name: Staging Manual DAGs and Requirements Sync

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to sync from"
        required: true
        default: "main"

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout specified branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::824587100790:role/GitHubEKSRole
          role-session-name: GitHubStagingSession
          aws-region: us-west-2

      - name: Sync DAGs to S3
        run: |
          aws s3 sync dags/ s3://mstack-staging-dags/dags/ --delete

      - name: Upload requirements.txt to S3
        run: |
          aws s3 cp requirements.txt s3://mstack-staging-dags/requirements.txt
