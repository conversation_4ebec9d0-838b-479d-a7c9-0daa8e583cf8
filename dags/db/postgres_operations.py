import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from typing import List, Dict, Any
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from functools import lru_cache

# --- Engine Caching ---
@lru_cache(maxsize=4) # Cache up to 4 different engine configurations (e.g., procure, sales, maybe others)
def get_cached_engine(db_url: str) -> Engine:
    """
    Creates and caches SQLAlchemy engines based on the database URL.
    Includes basic connection pool configuration.
    """
    logging.info(f"Creating or retrieving cached engine for URL ending in ...{db_url[-20:]}")
    # Recommended pool settings (adjust as needed):
    # - pool_size: Number of connections kept persistently in the pool.
    # - max_overflow: Max connections allowed beyond pool_size during peaks.
    # - pool_recycle: Time in seconds after which connections are recycled (prevents db timeouts). 3600 = 1 hour.
    # - pool_timeout: Time in seconds to wait for a connection from the pool before raising an error.
    engine = create_engine(
        db_url,
        poolclass=QueuePool,
        pool_size=50,         # Start with 5, adjust based on load
        max_overflow=50,     # Allow 10 more connections during peaks
        pool_recycle=1800,   # Recycle connections every 30 minutes
        pool_timeout=30      # Wait 30 seconds for a connection
    )
    return engine

class PostgresOperations:
    def __init__(self, connection_params: Dict[str, Any]):
        """
        Initialize PostgresSQL operations using a shared, cached engine.
        :param connection_params: Dictionary containing connection parameters
        """
        # Create SQLAlchemy URL from connection parameters
        url = f"postgresql://{connection_params.get('user')}:{connection_params.get('password')}@{connection_params.get('host')}:{connection_params.get('port')}/{connection_params.get('dbname')}"
        
        # Get potentially cached engine
        self.engine = get_cached_engine(url)
        
        # Sessionmaker uses the shared engine
        self.Session = sessionmaker(bind=self.engine)
        logging.info(f"PostgresOperations initialized using engine for ...{url[-20:]}")

    def get_connection(self) -> Engine:
        """Return the shared database engine"""
        return self.engine

    def read_data(self, query: str, params: dict = None) -> List[Dict[str, Any]]:
        """
        Read data from PostgresSQL using a session from the pool.
        :param query: SQL query to execute
        :param params: Query parameters (optional)
        :return: List of dictionaries containing the results
        """
        # The 'with' block ensures the session is closed and the underlying connection returned to the pool.
        with self.Session() as session:
            result = session.execute(text(query), params or {})
            # Use .mappings().all() for SQLAlchemy 2.0 style dict conversion
            return result.mappings().all() # More robust way to get list of dicts

    def write_data(self, query: str, params: dict = None) -> None:
        """
        Write data to PostgresSQL using a session from the pool.
        :param query: SQL query to execute
        :param params: Query parameters (optional)
        """
        # The 'with' block ensures the session is closed, committed/rolled back, 
        # and the underlying connection returned to the pool.
        with self.Session() as session:
            try:
                session.execute(text(query), params or {})
                session.commit()
            except Exception:
                logging.error("Error during write_data, rolling back session.")
                session.rollback()
                raise # Re-raise the exception after rollback

    def update_data(self, query: str, params: dict = None) -> None:
        """
        Update data in PostgresSQL. Reuses write_data.
        :param query: SQL query to execute
        :param params: Query parameters (optional)
        """
        self.write_data(query, params)

    def close(self) -> None:
        """
        NO LONGER disposes the engine. 
        The shared engine should persist. Disposal happens globally if needed,
        typically when the worker process shuts down.
        """
        logging.debug("PostgresOperations.close() called - NO-OP for shared engine.")
        # Intentionally left blank. Do NOT dispose the shared engine here.
        pass 