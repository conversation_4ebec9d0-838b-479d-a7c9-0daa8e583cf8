import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from typing import Dict, Any, Optional
from supabase import create_client, Client

class SupabaseOperations:
    def __init__(self, params: Dict[str, Any]):
        self.client: Client = create_client(params['url'], params['key'])
        self.bucket_name = params['bucket_name']

    def download_file(self, file_path: str) -> Optional[bytes]:
        """Download a file from Supabase storage and return the local file path"""
        try:
            # Download the file
            response: bytes = self.client.storage.from_(self.bucket_name).download(file_path)
            return response
        except Exception as e:
            logging.error(f"Error downloading file {file_path}: {str(e)}")
            return None

    def upload_file(self, file_bytes: bytes, destination_path: str, content_type: str) -> bool:
        """Upload a file to Supabase storage"""
        try:
            self.client.storage.from_(self.bucket_name).upload(
                destination_path,
                file_bytes,
                {"content-type": content_type}
            )
            return True
        except Exception as e:
            logging.error(f"Error uploading file to {destination_path}: {str(e)}")
            return False

    def check_file_exists(self, file_path: str) -> bool:
        """Check if a file exists in the Supabase bucket"""
        try:
            # List files in the directory to check existence
            parent_path = '/'.join(file_path.split('/')[:-1])
            file_name = file_path.split('/')[-1]
            
            files = self.client.storage.from_(self.bucket_name).list(parent_path)
            
            # Check if file exists in the list
            return any(f['name'] == file_name for f in files)
            
        except Exception as e:
            if "not_found" in str(e).lower():
                # Directory doesn't exist
                return False
            else:
                logging.warning(f"Error checking file existence: {file_path} - {str(e)}")
                return False

    def transfer_file(self, file_path: str, destination_supabase: 'SupabaseOperations', content_type: str = 'application/pdf') -> bool:
        """Transfer a file from this Supabase instance to another"""
        try:
            # Download file from source bucket
            file_bytes = self.download_file(file_path)
            if not file_bytes:
                logging.error(f"Failed to download file from {self.bucket_name}: {file_path}")
                return False

            # Upload file to destination bucket
            if not destination_supabase.upload_file(file_bytes, file_path, content_type):
                logging.error(f"Failed to upload file to {destination_supabase.bucket_name}: {file_path}")
                return False

            logging.info(f"Successfully transferred file from {self.bucket_name} to {destination_supabase.bucket_name}: {file_path}")
            return True

        except Exception as e:
            logging.error(f"Error transferring file {file_path}: {str(e)}")
            return False

