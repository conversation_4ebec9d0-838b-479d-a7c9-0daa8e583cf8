

def fix_dtype_mapping_for_pandas(dtype_map):
    fixed_map = {}
    for col, dtype in dtype_map.items():
        # Skip datetime columns
        if dtype.startswith("datetime"):
            continue
        # Use Pandas nullable types
        elif dtype == "int64":
            fixed_map[col] = "Int64"
        elif dtype == "float64":
            fixed_map[col] = "Float64"
        elif dtype == "bool":
            fixed_map[col] = "boolean"
        else:
            fixed_map[col] = dtype
    return fixed_map