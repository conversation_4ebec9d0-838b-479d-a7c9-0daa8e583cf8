"""
DAG to auto-create SAMPLE orders in LogiStack from ProcureStack.
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import Variable
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert

import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
import json
import logging
from decimal import Decimal
from datetime import timedelta, datetime, timezone
from typing import Dict, List, Optional
import requests
from config.db_config import DatabaseConfig
from db.postgres_operations import PostgresOperations
from db.mongo_operations import MongoOperations

from bson import ObjectId
# --- Constants ---
KEYSTONE_API_BASE_URL = Variable.get("keystone_api_base_url")
VALIDATE_PO_URL = Variable.get("validate_po_url")
ORDER_BOOK_URL = Variable.get("order_book_url")
CUSTOMER_ORDER_URL = Variable.get("customer_order_url")
SUPPLIER_ORDER_BOOK_URL = Variable.get("supplier_order_book_url")


default_args = {
    'owner': 'procurestack-team',
    'depends_on_past': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'retry_exponential_backoff': True,
}

class LogistackOrderManager:
    """Handles the end-to-end process of syncing sample orders from ProcureStack to Logistack."""
    def __init__(self) -> None:
        self.sales_db: Optional[PostgresOperations] = None
        self.procure_db: Optional[PostgresOperations] = None
        self.logi_db: Optional[MongoOperations] = None
        # self.supplier_cache: Dict[str, Dict] = {}
        logging.info("[Init] LogistackOrderManager initialized")

    # --- DB Connection Methods ---
    def initialize_connections(self) -> None:
        """Initializes all required DB connections."""
        logging.info("[Init] Initializing DB connections")
        try:
            self.sales_db = PostgresOperations(DatabaseConfig.get_sales_stack_postgres_params())
            self.procure_db = PostgresOperations(DatabaseConfig.get_procuro_stack_postgres_params())
            self.logi_db = MongoOperations(*DatabaseConfig.get_logi_stack_mongo_params())
            logging.info("[Init] Successfully initialized all DB connections")
        except Exception as e:
            logging.error(f"[Init][Error] Failed to initialize DB connections: {e}")
            raise

    def close_connections(self) -> None:
        """Closes all DB connections."""
        logging.info("[Close] Closing DB connections")
        try:
            if self.sales_db:
                self.sales_db.close()
            if self.procure_db:
                self.procure_db.close()
            if self.logi_db:
                self.logi_db.close()
            logging.info("[Close] All DB connections closed successfully")
        except Exception as e:
            logging.error(f"[Close][Error] Failed to close connections: {e}")

    # --- API Auth & Request Methods ---
    def get_request_headers(self, employee_id: str) -> Dict[str, str]:
        """Returns headers with entityId and entityType."""
        if not employee_id:
            raise ValueError("employee_id is required to make API requests")
        return {
            'entityId': employee_id,
            'entityType': 'employee',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

    def make_api_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Makes an API request with retry logic."""
        max_retries = 3
        retry_count = 0
        while retry_count < max_retries:
            try:
                kwargs.setdefault('timeout', 30)
                logging.info(f"[API Request] ==> {method} {url}")
                if 'headers' in kwargs:
                    logging.info(f"[API Request Headers] ==> {kwargs.get('headers', {})}")

                response = requests.request(method, url, verify=False, **kwargs)

                logging.info(f"[API Response] <== {response.status_code} from {method} {url}")
                try:
                    logging.info(f"[API Response Body] <== {json.dumps(response.json(), indent=2, default=str)}")
                except json.JSONDecodeError:
                    logging.info(f"[API Response Body] <== (non-JSON): {response.text}")
                
                # No 401 handling needed, caller should handle responses
                return response
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count == max_retries:
                    logging.error(f"[API][Error] Max retries ({max_retries}) exceeded")
                    raise
                logging.warning(f"[API] Request failed (attempt {retry_count}/{max_retries}): {e}")
                continue
        raise Exception(f"[API] Failed after {max_retries} attempts")

    def validate_po(self, customer_id: str, po_number: str, employee_id: str) -> bool:
        """Validates if PO already exists."""
        try:
            headers = self.get_request_headers(employee_id)
            payload = {"customerId": customer_id, "poNumber": po_number}
            logging.info(f"[Validate] Validating PO {po_number} for customer {customer_id}")
            response = self.make_api_request('POST', VALIDATE_PO_URL, json=payload, headers=headers)
            if response.status_code == 200:
                result = response.json()
                is_valid = result.get('isValid', False)
                if not is_valid:
                    logging.info(f"[Validate] PO {po_number} already exists for customer {customer_id}")
                return is_valid
            return False
        except Exception as e:
            logging.error(f"[Validate][Error] Failed to validate PO: {e}")
            return False

    # --- Data Fetch Methods ---
    def fetch_samples(self) -> List[Dict]:
        """Fetches samples that have not been sent to Logistack."""
        query = """
            SELECT 
                sr.id AS sample_id,
                sr.created_at AS sample_request_date,
                sr.status,
                e.target_price_currency AS currency,
                sr.quantity_unit AS unit,
                e.category,
                e.enquiry_id,
                sr.quantity,
                sr.delivery_address AS delivery_address,
                c.chemical_name,
                p.full_name AS procurement_poc,
                p.email AS procurement_poc_email
            FROM sample_requests sr
            LEFT JOIN enquiries e ON e.id::text = sr.enquiry_id::text
            LEFT JOIN chemicals c ON c.id = e.chemical_id
            LEFT JOIN profiles p ON CAST(p.id AS text) = e.procurement_poc_id
            WHERE sr.status = 'sample_request_raised' 
              AND sr.id IS NOT NULL 
              AND (sr.is_sent_to_logistack IS NULL OR sr.is_sent_to_logistack = FALSE)
            ORDER BY sr.created_at DESC
        """
        try:
            rows = self.procure_db.read_data(query, {})
            logging.info(f"[DEBUG] fetch_samples returned {len(rows)} rows: {rows}")
            return rows
        except Exception as e:
            logging.error(f"[Samples][Error] Failed to fetch samples: {e}")
            raise

    def mark_sample_as_sent(self, sample_id: str) -> None:
        """Marks a sample as sent to LogiStack."""
        try:
            logging.info(f"[DEBUG] Attempting to mark as sent: sample_id={sample_id} (type={type(sample_id)})")
            query = "UPDATE sample_requests SET is_sent_to_logistack = TRUE WHERE id = :sample_id"
            self.procure_db.write_data(query, {"sample_id": sample_id})
            logging.info(f"[Update] Marked sample {sample_id} as sent to LogiStack")
        except Exception as e:
            logging.error(f"[Update][Error] Failed to mark sample {sample_id} as sent: {e}")

    def fetch_employee_id_by_email(self, email: str) -> Optional[str]:
        """Fetches employee _id from LogiStack MongoDB by email."""
        if not email:
            logging.warning("[Mongo] No email provided to fetch employee ID.")
            return None
        try:
            employee = self.logi_db.read_data(
                collection_name='employee',
                query={"email": email, "deleted": False}
            )
            if employee:
                employee_id = str(employee[0]['_id'])
                logging.info(f"[Mongo] Found employee_id {employee_id} for email {email}")
                return employee_id
            else:
                logging.warning(f"[Mongo] No employee found for email: {email}")
                return None
        except Exception as e:
            logging.error(f"[Mongo][Error] Failed to fetch employee for email {email}: {e}")
            return None

    def fetch_product_details_for_payload(self, enquiry_id: str) -> Optional[Dict]:
        """Fetches product details for payload construction."""
        query = """
           SELECT DISTINCT 
                c.product_id,
                e.category
            FROM enquiries e
            JOIN chemicals c ON c.id = e.chemical_id
            WHERE e.enquiry_id = :enquiry_id;
        """
        try:
            result = self.procure_db.read_data(query, {"enquiry_id": enquiry_id})
            if not result or not result[0].get("product_id"):
                logging.warning(f"No product_id found for enquiry_id: {enquiry_id}")
                return None
            product_id = result[0]["product_id"]
            product = self.logi_db.read_data(
                collection_name='product',
                query={"productId": product_id},
            )
            product = product[0] if product else None
            if not product:
                logging.warning(f"No product found in MongoDB for productId: {product_id}")
                return None
            product["productId"] = product_id
            return product
        except Exception as e:
            logging.error(f"[fetch_product_details_for_payload][Error]: {e}")
            return None

    def fetch_customers(self, enquiry_ids: List[str]) -> Dict[str, Dict]:
        """Fetches customers for a list of enquiry_ids."""
        if not enquiry_ids:
            return {}
        query = """
            SELECT e.enquiry_id, c.customer_id
            FROM enquiries e
            LEFT JOIN customer c ON e.customer_id = c.id
            WHERE e.enquiry_id = ANY(:enquiry_ids)
        """
        try:
            rows = self.sales_db.read_data(query, {"enquiry_ids": enquiry_ids})
            enquiry_to_customer_id = {r['enquiry_id']: r['customer_id'] for r in rows if r.get('customer_id')}
            customer_ids = list(enquiry_to_customer_id.values())
            if not customer_ids:
                logging.warning("[Customers] No customer_ids found for given enquiry_ids")
                return {}
            customers = self.logi_db.read_data(
                collection_name='customer',
                query={
                    "customerId": {"$in": customer_ids},
                    "deleted": False
                }
            )
            # logging.info(f"[DEBUG] MongoDB customers fetched: {json.dumps(customers, default=str, indent=2)}")
            customer_map = {c['customerId']: c for c in customers}
            return {
                enquiry_id: customer_map[customer_id]
                for enquiry_id, customer_id in enquiry_to_customer_id.items()
                if customer_id in customer_map
            }
        except Exception as e:
            logging.error(f"[Customers][Error] Failed to fetch customers: {e}")
            raise

    def fetch_supplier_details(self, supplier_id: str) -> Optional[Dict]:
        """Fetches supplier details from MongoDB, with caching."""
        if not supplier_id:
            return None
        if supplier_id in self.supplier_cache:
            return self.supplier_cache[supplier_id]
        try:
            supplier = self.logi_db.read_data(
                collection_name='supplier',
                query={"supplierId": supplier_id, "deleted": False}
            )
            if supplier:
                self.supplier_cache[supplier_id] = supplier[0]
                return supplier[0]
            return None
        except Exception as e:
            logging.error(f"[Supplier][Error] Failed to fetch supplier {supplier_id}: {e}")
            return None

    def fetch_supplier_for_sample(self, sample_id: str, enquiry_id: str) -> Optional[Dict]:
        """Fetches supplier_id for a given sample and its details from MongoDB."""
        query = """
            SELECT
                sr.id AS sample_id,
                sr.enquiry_id as en_id,
                e.enquiry_id,
                s.supplier_id
            FROM sample_requests sr
            LEFT JOIN enquiries e ON sr.enquiry_id = e.id
            LEFT JOIN suppliers s ON sr.supplier_id = s.id
            WHERE sr.id = :sample_id AND (s.supplier_id IS NOT NULL)
        """
        try:
            rows = self.procure_db.read_data(query, {"sample_id": sample_id})
            if not rows or not rows[0].get("supplier_id"):
                logging.info(f"[Supplier] No supplier_id found for sample_id: {sample_id}")
                return None
            supplier_id = rows[0]["supplier_id"]
            supplier = self.fetch_supplier_details(supplier_id)
            if not supplier:
                logging.warning(f"[Supplier] No supplier details found in MongoDB for supplier_id: {supplier_id}")
                return None
            return supplier
        except Exception as e:
            logging.error(f"[Supplier][Error] Failed to fetch supplier for sample {sample_id}: {e}")
            return None

    def build_supplier_order_payload(self, supplier: Dict, customer_obj_id: str, products: List[Dict]) -> str:
        """Builds the payload for Supplier Order Book API using the customer object's id."""
        return json.dumps({
            "products": [],
            "supplier": {
                "id": str(supplier.get("_id")),
                "supplierId": supplier.get("supplierId")
            },
            "linkedCOBs": [customer_obj_id]
        })

    def send_supplier_order(self, payload: Dict, employee_id: str) -> bool:
        """Sends the supplier order to the Supplier Order Book API."""
        try:
            headers = self.get_request_headers(employee_id)
            resp = self.make_api_request('POST', SUPPLIER_ORDER_BOOK_URL, data=payload, headers=headers)

            try:
                logging.info(f"[API][SupplierOrderBook] Response: {resp.text}")
            except Exception:
                logging.info(f"[API][SupplierOrderBook] Response: {resp.text}")
            resp.raise_for_status()
            return True
        except Exception as e:
            logging.error(f"[API][SupplierOrderBook][Error] Failed to create supplier order: {e}")
            return False

    # --- Payload Construction ---
    def build_payload(self, sample: Dict, customer: Dict, product: Dict) -> Dict:
        """Builds the payload for LogiStack API from sample, customer, and product data."""
        def to_iso8601(dt) -> Optional[str]:
            if dt is None:
                return None
            if isinstance(dt, str):
                try:
                    dt = datetime.fromisoformat(dt)
                except ValueError:
                    dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S.%f")
            return dt.replace(tzinfo=timezone.utc).isoformat(timespec="milliseconds")
        UOM_MAPPING = {
            "Metric Ton (mt)": "METRIC_TON",
            "Kilogram (Kg)": "KILOGRAM",
            "Kilolitre (Kl)": "KILOLITRE",
            "Litre (L)": "LITRE",
            "Pound (lb)": "POUND",
            "Gallon (gal)": "GALLON",
        }
        unit = sample.get("unit") or ""
        mapped_uom = UOM_MAPPING.get(unit)
        customer_country = customer.get('address', {}).get('country', '')
        country_note = ""
        if customer_country:
            country_note = "This has been added from enquiry, please change if required"
        payload = {
            "orderType": "SAMPLE",
            "products": [{
                "product": {
                    "id": str(product.get("_id")),
                    "productId": product.get("productId"),
                    "tradeName": product.get("tradeName"),
                    "grade": product.get("grade") or "",
                    "technicalName": product.get("technicalName") or "",
                    "synonyms": product.get("synonyms") or [],
                    "categories": [{
                        "category": product.get("category") or "",
                        "subCategory": product.get("subCategory") or ""
                    }],
                    "functions": [],
                    "family": product.get("family") or [],
                    "endUses": [],
                    "features": [],
                    "specifications": None,
                    "documents": product.get("documents") or {},
                    "deleted": bool(product.get("deleted", False)),
                    "casNumber": product.get("casNumber") or "",
                    "createdBy": product.get("createdBy") or "",
                    "lastUpdatedBy": product.get("lastUpdatedBy") or "",
                    "createdAt": to_iso8601(product.get("createdAt")) if product.get("createdAt") else None,
                    "lastUpdatedAt": to_iso8601(product.get("lastUpdatedAt")) if product.get("lastUpdatedAt") else None,
                },
                "uom": mapped_uom,
                "quantity": float(Decimal(sample.get("quantity"))) if sample.get("quantity") is not None else 1,
                "testingRequired": False,
                "documents": {}
            }],
            "customer": {
                "id": str(customer.get("_id")),
                "customerId": customer.get("customerId"),
                "name": customer.get("name"),
                "address": {
                    "street": customer.get("address", {}).get("street") or "",
                    "city": customer.get("address", {}).get("city") or "",
                    "state": customer.get("address", {}).get("state") or "",
                    "postalCode": customer.get("address", {}).get("postalCode") or "",
                    "country": customer.get("address", {}).get("country") or "IN",
                    "addressAsString": ""
                },
                "email": customer.get("email") or "",
                "countryCode": customer.get("countryCode") or "",
                "companySize": None,
                "mobile": customer.get("mobile") or "",
                "accountOwner": customer.get("accountOwner") or "",
                "categories": customer.get("categories") or [],
                "type": "END_CUSTOMER",  
                "remarks": customer.get("remarks") or [],
                "l1Reviewers": customer.get("l1Reviewers") or [],
                "l2Reviewers": customer.get("l2Reviewers") or [],
                "deleted": bool(customer.get("deleted", False)),
                "createdBy": customer.get("createdBy") or "",
                "lastUpdatedBy": customer.get("lastUpdatedBy") or "",
                "createdAt": to_iso8601(customer.get("createdAt")) if customer.get("createdAt") else None,
                "lastUpdatedAt": to_iso8601(customer.get("lastUpdatedAt")) if customer.get("lastUpdatedAt") else None,
                "documents": customer.get("documents") or {}
            },
            "purchaseOrderNumber": sample.get("sample_id"),
            "purchaseOrderDate": to_iso8601(sample.get("sample_request_date")) if sample.get("sample_request_date") else None,
            "category": sample.get("category") or "",
            "deliveryAddress": sample.get("delivery_address") or "",
            # Add country of delivery note if needed
            "countryOfDelivery": sample.get("customer_country") or "",
            "countryOfDeliveryNote": country_note,
            # Billing address and Sample Requested by left blank
            "billingAddress": "",
            "sampleRequestedBy": ""
        }
        logging.info(f"[DEBUG] Payload: {json.dumps(payload, default=str, indent=2)}")
        return payload

    # --- Order Sending ---
    def send_order(self, payload: Dict, sample_id: str, employee_id: str, **kwargs) -> bool:
        """Sends the order to LogiStack APIs with error handling, and creates supplier order if supplier_id exists."""
        try:
            customer_id = payload['customer']['customerId']
            po_number = payload['purchaseOrderNumber']
            logging.info(f"[API][Request] Preparing to send order for sample {customer_id} || {po_number}")
            is_valid_po = self.validate_po(customer_id, po_number, employee_id)
        
            if is_valid_po:
                logging.info(f"[API][Validation] PO is VALID for sample {sample_id}. Proceeding with Order creation APIs.")
                headers = self.get_request_headers(employee_id)
                resp1 = self.make_api_request('POST', ORDER_BOOK_URL, json=payload, headers=headers)
                if resp1.status_code == 400:
                    try:
                        error_msg = resp1.json().get('message', 'No error details provided')
                    except Exception as e:
                        send_exception_slack_alert(e, context_info=kwargs)
                        error_msg = resp1.text
                    logging.error(f"[API][Validation] Bad Request Error: {error_msg}")
                    if 'product' in error_msg.lower():
                        logging.error("[API][Validation] Product data validation failed")
                    if 'customer' in error_msg.lower():
                        logging.error("[API][Validation] Customer data validation failed")
                    if 'uom' in error_msg.lower():
                        logging.error(f"[API][Validation] Invalid UOM: {payload['products'][0]['uom']}")
                    if 'quantity' in error_msg.lower():
                        logging.error(f"[API][Validation] Invalid quantity: {payload['products'][0]['quantity']}")
                    return False
                resp1.raise_for_status()
                order_id = resp1.json().get('id')
                # Create Customer Order
                customer_obj_id = resp1.json().get('id')  # Use customer object's id for supplier order
                try:
                    resp2 = self.make_api_request('POST', CUSTOMER_ORDER_URL, json=payload, headers=headers)
                    try:
                        resp2_json = resp2.json()
                    except ValueError:
                        logging.info(f"[API][CustomerOrder] Response: {resp2.text}")
                    resp2.raise_for_status()
                    logging.info(f"[API] Successfully created CustomerOrder {resp2_json.get('id')} for sample {sample_id}")
                except Exception as e:
                    send_exception_slack_alert(e, context_info=kwargs)
                    logging.error(f"[API][CustomerOrder][Error] Failed to create CustomerOrder for sample {sample_id}: {e}")
                    return False
                # --- Supplier Order Logic ---
                # supplier = self.fetch_supplier_for_sample(sample_id, payload.get('enquiry_id'))
                # if supplier and customer_obj_id:
                #     supplier_payload = self.build_supplier_order_payload(supplier, customer_obj_id, payload.get('products'))
                #     supplier_result = self.send_supplier_order(supplier_payload, employee_id )
                #     if not supplier_result:
                #         logging.error(f"[SupplierOrder] Failed to create supplier order for sample {sample_id}")
                #         return False
                self.mark_sample_as_sent(sample_id)
                return True
               
        except requests.exceptions.HTTPError as e:
            logging.error(f"[API][Error] HTTP Error for sample {sample_id}: {e}")
            if e.response is not None:
                try:
                    error_detail = e.response.json()
                    logging.error(f"[API][Error] Error details: {json.dumps(error_detail, indent=2)}")
                    if 'errors' in error_detail:
                        for field, errors in error_detail['errors'].items():
                            logging.error(f"[API][Validation] Field '{field}' errors: {', '.join(errors)}")
                except ValueError:
                    logging.error(f"[API][Error] Response text: {e.response.text}")
            return False
        except Exception as e:
            logging.error(f"[API][Error] Unexpected error for sample {sample_id}: {e}", exc_info=True)
            return False

    # --- Main Run ---
    def run(self) -> None:
        """Main entry point for processing and syncing sample orders."""
        try:
            logging.info("--- Starting Sample Order Sync Run ---")
            self.initialize_connections()
            samples = self.fetch_samples()
            if not samples:
                logging.info("[Run] No new samples to process. Exiting.")
                return

            logging.info(f"[Run] Fetched {len(samples)} potential samples to process.")
            success_count = 0
            for i, sample in enumerate(samples):
                sample_id = sample.get('sample_id')
                enquiry_id = sample.get('enquiry_id')
                logging.info(f"--- Processing Sample {i+1}/{len(samples)}: [Sample ID: {sample_id}, Enquiry ID: {enquiry_id}] ---")
                try:
                    if sample.get('is_sent_to_logistack') is True:
                        logging.info(f"[Skip] Sample {sample_id} already marked as sent.")
                        continue

                    # 1. Fetch Employee
                    procurement_poc_email = sample.get('procurement_poc_email')
                    if not procurement_poc_email:
                        logging.warning(f"[FAIL] Sample {sample_id}: Missing 'procurement_poc_email'. Skipping.")
                        continue
                    logging.info(f"Step 1: Fetching employee for email: {procurement_poc_email}")
                    employee_id = self.fetch_employee_id_by_email(procurement_poc_email)
                    if not employee_id:
                        logging.warning(f"[FAIL] Sample {sample_id}: Could not find employee_id for email {procurement_poc_email}. Skipping.")
                        continue
                    logging.info(f"  > Success. Found employee_id: {employee_id}")

                    # 2. Fetch Customer
                    if not enquiry_id:
                        logging.warning(f"[FAIL] Sample {sample_id}: Missing 'enquiry_id'. Skipping.")
                        continue
                    logging.info(f"Step 2: Fetching customer for enquiry_id: {enquiry_id}")
                    customer_map = self.fetch_customers([enquiry_id])
                    customer = customer_map.get(enquiry_id)
                    if not customer:
                        logging.warning(f"[FAIL] Sample {sample_id}: Could not find customer for enquiry_id: {enquiry_id}. Skipping.")
                        continue
                    logging.info(f"  > Success. Fetched customer: {customer.get('customerId')}")

                    # 3. Fetch Product
                    logging.info(f"Step 3: Fetching product details for enquiry_id: {enquiry_id}")
                    product_details = self.fetch_product_details_for_payload(enquiry_id)
                    if not product_details:
                        logging.warning(f"[FAIL] Sample {sample_id}: No product details found for enquiry_id {enquiry_id}. Skipping.")
                        continue
                    logging.info(f"  > Success. Fetched product: {product_details.get('productId')}")

                    # 4. Build Payload
                    logging.info("Step 4: Building payload.")
                    payload = self.build_payload(sample, customer, product_details)
                    logging.info("  > Success. Payload built.")

                    # 5. Send Order
                    logging.info(f"Step 5: Sending order for sample {sample_id}")
                    result = self.send_order(payload, str(sample_id), employee_id)
                    logging.info(f"  > Result for {sample_id}: {'SUCCESS' if result else 'FAILURE'}")

                    if result:
                        success_count += 1
                except Exception as e:
                    logging.error(f"[FATAL_SAMPLE_ERROR] Sample {sample_id} failed with unexpected error: {e}", exc_info=True)
                    continue
            logging.info(f"--- Run Finished. Successfully processed {success_count}/{len(samples)} samples ---")
            logging.info("[Run] Sample orders created in DRAFT status - users need to complete mandatory fields:")
            logging.info("[Run] - Packaging details")
            logging.info("[Run] - Testing flag (required/not required)")
            logging.info("[Run] - Documents (TDS)")
            logging.info("[Run] Users can search orders by sample number, customer name in LogiStack dashboard")
        except Exception as e:
            logging.error(f"[Run][Fatal] Fatal error: {e}")
            raise
        finally:
            self.close_connections()

# --- Airflow DAG Entry Point ---
def run() -> None:
    """Entry point for the Airflow task."""
    manager = LogistackOrderManager()
    manager.run()

with DAG(
    "procure_to_logi_sample_order_sync_dag",
    default_args={
        'on_failure_callback':send_slack_alert 
    },
    description="Auto-create SAMPLE orders in LogiStack from ProcureStack data",
    schedule="*/10 * * * *",  # Every 10 minutes,
    catchup=False,
    tags=["logistack", "sample_order", "production"],
    max_active_runs=1,
) as dag:
    create_sample_orders_task = PythonOperator(
        task_id="create_sample_orders",
        python_callable=run,
        execution_timeout=timedelta(minutes=30),
        retries=2,
        retry_delay=timedelta(minutes=2)
    )






