import logging
import os
from datetime import datetime, timezone
from airflow import DAG
from db.mongo_operations import MongoOperations
from config.default import DEFAULT_DAG_ARGS
from config.db_config import DatabaseConfig
from ses_hook import SESHook
from jinja2 import FileSystemLoader, Environment

# Path to templates directory
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')

# Define the mapping of groups to their heads (name and email)
GROUP_HEAD_MAPPING = {
    # Format: 'group_name': {'name': 'Head Name', 'email': '<EMAIL>'}
    'source': {'name': 'Coatings Team Lead', 'email': '<EMAIL>'},
    'documentation': {'name': 'Procurement Manager', 'email': '<EMAIL>'},
    'finance': {'name': 'Shipping Department Head', 'email': '<EMAIL>'},
    'logistics': {'name': 'Logistics Manager', 'email': '<EMAIL>'},
    # Add more mappings as needed
    # Default fallback for any unmapped groups
    'DEFAULT': {'name': 'Tech', 'email': '<EMAIL>'}
}



# Aggregation pipeline for group head emails
group_delayed_tasks_pipeline = [
    {
        "$match": {
            'dueDate': {'$lt': datetime.now(tz=timezone.utc)},
            'deleted': False,
            'status': {'$ne': "COMPLETED"}
        }
    },
    {
        "$set": {
            "assignedTo": {
                "$convert": {
                    "input": "$assignedTo",
                    "to": "objectId",
                    "onError": None,
                    "onNull": None
                }
            }
        }
    },
    {
        "$lookup": {
            "from": "employee",
            "localField": "assignedTo",
            "foreignField": "_id",
            "as": "assignedToDetails"
        }
    },
    {
        "$unwind": {
            "path": "$assignedToDetails",
            "preserveNullAndEmptyArrays": False
        }
    },
    {
        "$group": {
            "_id": "$group",
            "group_name": {"$first": "$group"},
            "employees": {
                "$addToSet": {
                    "id": "$assignedToDetails._id", 
                    "name": "$assignedToDetails.name",
                    "email": "$assignedToDetails.email"
                }
            },
            "activities": {
                "$push": {
                    "entityType": "$entityType",
                    "status": "$status",
                    "entityId": "$entityId",
                    "orderId": "$orderId",
                    "name": "$name",
                    "assignedToId": "$assignedTo",
                    "assignedToName": "$assignedToDetails.name",
                    "assignedToEmail": "$assignedToDetails.email",
                    "group": "$group",
                    "dueDate": "$dueDate"
                }
            }
        }
    },
    {
        "$project": {
            "_id": 0,
            "group_name": 1,
            "employees": 1,
            "activities": 1
        }
    }
]

def get_group_delayed_tasks():
    """Get all tasks from MongoDB grouped by their group field with group head details"""
    # Initialize MongoDB connection using MongoOperations
    mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
    mongo_ops = MongoOperations(mongo_conn_string, mongo_db)
    
    try:
        logging.info("Running aggregation query for group delayed tasks")
        # Use the aggregate function with our pipeline
        results = mongo_ops.aggregate(group_delayed_tasks_pipeline, collection_name="activity")
        
        # Log the count of results
        result_count = len(results)
        logging.info(f"Found {result_count} groups with delayed tasks")
        
        if result_count == 0:
            logging.warning("No delayed tasks found for any groups")
            
        return results
    except Exception as e:
        logging.error(f"Error retrieving group delayed tasks: {str(e)}")
        # Return empty list in case of error
        return []
    finally:
        # Make sure to close the connection
        mongo_ops.close()

def generate_group_html_report(**context):
    """Generate HTML reports for each group head with their group's delayed tasks"""
    delayed_tasks_by_group = get_group_delayed_tasks()

    logging.info(f"Retrieved delayed tasks grouped by group")

    # Create a dictionary to store HTML content for each group head
    group_head_reports = {}
    
    if not delayed_tasks_by_group:
        logging.warning("No group delayed tasks found to report")
        return {}
    
    logging.info(f"Processing delayed tasks for {len(delayed_tasks_by_group)} groups")
    
    # Set up Jinja2 environment with the template directory
    jinja_env = Environment(loader=FileSystemLoader(TEMPLATE_DIR))
    template = jinja_env.get_template('group_head_email_template.html')
    
    for group_data in delayed_tasks_by_group:
        # Handle null group name by using a default display name
        group_name = group_data.get('group_name')
        if group_name is None or group_name == "":
            group_name = "Unassigned Group"
            logging.warning(f"Found tasks with null or empty group name, using '{group_name}' as display name")
        
        # Get group head details from the mapping, always use DEFAULT for null/empty group names
        if group_name is None or group_name == "":
            group_head_details = GROUP_HEAD_MAPPING['DEFAULT']
        else:
            group_head_details = GROUP_HEAD_MAPPING.get(group_name.lower(), GROUP_HEAD_MAPPING['DEFAULT'])
        
        group_head_name = group_head_details['name']
        group_head_email = group_head_details['email']
        
        logging.info(f"Using group head {group_head_name} <{group_head_email}> for group '{group_name}'")
        
        activities = group_data.get('activities', [])
        if not activities:
            logging.info(f"No activities found for group {group_name}")
            continue
            
        logging.info(f"Processing {len(activities)} delayed tasks for group {group_name}")
        
        # Organize tasks by employee
        employee_tasks = {}
        
        for activity in activities:
            employee_name = activity.get('assignedToName', 'Unknown Employee')
            
            if employee_name not in employee_tasks:
                employee_tasks[employee_name] = []
                
            try:
                due_date_str = activity.get('dueDate').strftime('%Y-%m-%d %H:%M:%S UTC') if activity.get('dueDate') else 'N/A'
            except Exception as e:
                logging.error(f"Error formatting date for activity: {str(e)}")
                due_date_str = 'Error with date'
                
            employee_tasks[employee_name].append({
                'entityId': activity.get('entityId', 'N/A'),
                'orderId': activity.get('orderId', 'N/A'),
                'entityType': activity.get('entityType', 'N/A'),
                'dueDate': due_date_str,
                'status': activity.get('status', 'N/A'),
                'assignedToName': activity.get('assignedToName', 'Unknown'),
                'assignedToEmail': activity.get('assignedToEmail', '<EMAIL>')
            })
        
        # Sort tasks within each employee by due date
        for employee, tasks in employee_tasks.items():
            employee_tasks[employee] = sorted(
                tasks, 
                key=lambda x: x['dueDate'] if x['dueDate'] != 'N/A' and x['dueDate'] != 'Error with date' else '9999-12-31'
            )
        
        # Generate HTML for this group head
        try:
            html_content = template.render(
                group_name=group_name,
                generation_time=datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
                employees=group_data.get('employees', []),
                employee_tasks=employee_tasks,
                total_tasks=len(activities)
            )
            
            # Store the HTML report for this group head
            group_head_reports[group_head_email] = {
                'name': group_head_name,
                'html': html_content,
                'group': group_name
            }
            logging.info(f"Generated HTML report for group head of {group_name}")
        except Exception as e:
            logging.error(f"Error generating HTML for group {group_name}: {str(e)}")
    
    logging.info(f"Generated reports for {len(group_head_reports)} group heads")
    # Return the dictionary of reports
    return group_head_reports

def send_group_head_email(**context):
    """Send personalized emails to group heads using AWS SES"""
    # Get the group head reports from the previous task
    group_head_reports = context['task_instance'].xcom_pull(task_ids='generate_group_html_report')
    
    if not group_head_reports:
        logging.warning("No group head reports to send")
        return {'sent': 0, 'failed': 0, 'details': {}}
    
    aws_config = DatabaseConfig.get_aws_ses_params()
    sender_email = aws_config['sender_email']

    # Create an instance of SESHook
    ses_hook = SESHook()
    # Track results
    results = {}
    sent_count = 0
    failed_count = 0
    
    # Send individual emails to each group head
    for email, report_data in group_head_reports.items():
        group_name = report_data.get('group', 'Group')
        subject = f"{group_name} Group Delayed Tasks Report - Action Required"
        
        try:
            response = ses_hook.send_email(
                sender=sender_email,
                recipient=[email],
                subject=subject,
                html_body=report_data['html']
            )
            results[email] = f"Success: MessageId={response.get('MessageId', 'unknown')}"
            sent_count += 1
        except Exception as e:
            results[email] = f"Failed: {str(e)}"
            failed_count += 1
    
    # Log summary of email sending
    summary = {
        'sent': sent_count,
        'failed': failed_count,
        'details': results
    }
    logging.info(f"Group head email sending summary: {summary}")
    return summary

with DAG(
    'group_head_email_dag',
    default_args=DEFAULT_DAG_ARGS,
    description='DAG to send personalized emails to group heads for delayed tasks',
    schedule='0 0 * * *',  # Run at midnight every day
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['monitoring', 'email', 'groups'],
) as dag:

    from airflow.operators.python import PythonOperator

    generate_group_report = PythonOperator(
        task_id='generate_group_html_report',
        python_callable=generate_group_html_report,
        
    )
    
    send_group_head_email_task = PythonOperator(
        task_id='send_group_head_email',
        python_callable=send_group_head_email,
        
    )

    generate_group_report >> send_group_head_email_task 