
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import logging
import pandas as pd
import time
from collections import OrderedDict
from common.etl_utils import sales_etl_dtypes
from common.common import fix_dtype_mapping_for_pandas
load_dotenv()

class SalesETL:
    def __init__(self):
        reporting_stack_postgres_params = DatabaseConfig.get_reporting_stack_postgres_params()
        sales_stack_postgres_params = DatabaseConfig.get_sales_stack_postgres_params()
        self.source_conn = PostgresOperations(sales_stack_postgres_params)
        self.destination_conn = PostgresOperations(reporting_stack_postgres_params)
        self.exculed_columns = ['roles', 'pipeline_stages']
        self.all_times = [
            'last_status_change',
            'target_quotation_date',
            'po_delivery_time',
            'expiry_date',
            'banned_until',
            'date',
            'updated_on'
        ]
        # Tables ordered based on foreign key dependencies (parent tables first)
        self.timestamp_keys = OrderedDict({
            # Independent tables (no foreign keys)
            'user_roles': 'created_at',
            'customer': 'modified_at',
            
            # Customer quotations (referenced by enquiry_quotations)
            'customer_quotations': 'updated_at',
            
            # Enquiries (many tables depend on this)
            'enquiries': 'last_status_change',
            
            # Tables dependent on enquiry
            'enquiry_clarifications': 'created_at',
            'enquiry_documents': 'created_at',
            'enquiry_status_history': 'created_at',
            'enquiry_quotations': 'created_at',
            'sample_requests': 'created_at',
            'quotation_feedback': 'created_at',
            'quote_generation_details': 'generated_at',
            'purchase_orders': 'created_at',
            
            # Tables dependent on sample_requests
            'sample_requests_attachments': 'created_at',
            'sample_feedback': 'created_at',
            'sample_status_history': 'changed_at',
            
            # Tables dependent on quotation_feedback
            'quotation_feedback_attachments': 'created_at',
            
            # Tables dependent on sample_feedback
            'sample_feedback_attachments': 'created_at',
            
            # Tables dependent on quote_generation_details
            'quote_generation_options': 'updated_at',
            'quote_generation_attachments': 'created_at',
            
            # Tables dependent on purchase_orders
            'purchase_order_attachments': 'created_at'
        })

    import psycopg2

    def get_all_table_ddls(self, source_engine, schema='public'):
        conn = source_engine.raw_connection()
        ddl_dict = {}

        ddl_dict = {}
        all_pk_cols = {}
        schema_source = 'sales'

        with conn.cursor() as cur:
            # Step 1: Get all table names in the schema
            cur.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s AND table_type = 'BASE TABLE';
            """, (schema,))
            tables = [row[0] for row in cur.fetchall()]

            # Step 2: For each table, build DDL
            for table_name in tables:
                # Initialize DDL statement
                ddl = f"CREATE TABLE IF NOT EXISTS {schema_source}.{table_name} (\n"

                # Step 2.1: Get column definitions
                cur.execute("""
                    SELECT
                        column_name,
                        pg_catalog.format_type(a.atttypid, a.atttypmod) AS data_type,
                        is_nullable,
                        column_default
                    FROM
                        pg_attribute a
                        JOIN pg_class c ON a.attrelid = c.oid
                        JOIN pg_namespace n ON c.relnamespace = n.oid
                        LEFT JOIN information_schema.columns i ON i.column_name = a.attname
                            AND i.table_schema = n.nspname
                            AND i.table_name = c.relname
                    WHERE
                        c.relname = %s
                        AND n.nspname = %s
                        AND a.attnum > 0
                        AND NOT a.attisdropped
                    ORDER BY
                        a.attnum;
                """, (table_name, schema))

                columns = cur.fetchall()
                column_defs = []
                for col in columns:
                    col_name, col_type, is_nullable, col_default = col
                    col_def = f"    {col_name} {col_type}"
                    if is_nullable == 'NO':
                        col_def += " NOT NULL"
                    if col_default is not None:
                        col_def += f" DEFAULT {col_default}"
                    column_defs.append(col_def)
                ddl += ",\n".join(column_defs)

                # Step 2.2: Add primary key constraint
                cur.execute("""
                    SELECT
                        a.attname AS column_name
                    FROM
                        pg_index i
                        JOIN pg_attribute a ON a.attrelid = i.indrelid
                            AND a.attnum = ANY(i.indkey)
                    WHERE
                        i.indisprimary
                        AND i.indrelid = %s::regclass;
                """, (table_name,))
                pk_columns = [row[0] for row in cur.fetchall()]
                if pk_columns:
                    all_pk_cols[table_name] = pk_columns
                    pk_def = f",\n    PRIMARY KEY ({', '.join(pk_columns)})"
                    ddl += pk_def

                # Step 2.3: Add foreign key constraints
                cur.execute("""
                    SELECT
                        kcu.column_name AS column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name,
                        tc.constraint_name AS constraint_name
                    FROM
                        information_schema.table_constraints AS tc
                        JOIN information_schema.key_column_usage AS kcu
                            ON tc.constraint_name = kcu.constraint_name
                        JOIN information_schema.constraint_column_usage AS ccu
                            ON ccu.constraint_name = tc.constraint_name
                    WHERE
                        tc.constraint_type = 'FOREIGN KEY'
                        AND tc.table_schema = %s
                        AND tc.table_name = %s;
                """, (schema, table_name))
                fk_constraints = cur.fetchall()
                for fk in fk_constraints:
                    col_name, ref_table, ref_column, constraint_name = fk
                    fk_def = f",\n    CONSTRAINT {constraint_name} FOREIGN KEY ({col_name}) REFERENCES {schema_source}.{ref_table}({ref_column})"
                    ddl += fk_def

                # Close the table definition
                ddl += "\n);"
                ddl_dict[table_name] = ddl


            conn.close()
            return ddl_dict

    def create_all_tables_dynamic(self):
        al_t = self.get_all_table_ddls(self.source_conn.engine)
        target_engine = self.destination_conn.engine
        max_retries = 5
        for k,v in al_t.items():
            for attempt in range(max_retries):
                try:
                    target_con = target_engine.raw_connection()
                    with target_con.cursor() as cur:
                        vv = v.replace('uuid NOT NULL DEFAULT gen_random_uuid()', 'VARCHAR(2600) NOT NULL')\
                            .replace('DEFAULT uuid_generate_v4()', '')\
                            .replace("enquiry_lifecycle_status NOT NULL DEFAULT 'enquiry_created'::enquiry_lifecycle_status",'varchar(2600)')\
                            .replace('unit_type', 'varchar(2600)')\
                            .replace('enquiry_lifecycle_status NOT NULL', 'varchar(2600)')\
                            .replace('app_role NOT NULL', 'varchar(2600)')\
                            .replace('uuid NOT NULL DEFAULT uuid_generate_v4()', 'varchar(2600) NOT NULL')\
                            .replace('uuid NOT NULL', 'varchar(2600) NOT NULL')\
                            .replace('uuid DEFAULT gen_random_uuid() NOT NULL','VARCHAR(2600) NOT NULL')\
                            .replace('uuid,', 'VARCHAR(2600),')
                        cur.execute(vv)
                    target_con.commit()
                    target_con.close()
                    break  # Success - exit retry loop
                    
                except Exception as e:
                    print(f"Attempt {attempt + 1} failed for table {k}")
                    print(f"Error: {str(e)}")
                    if attempt == max_retries - 1:  # Last attempt
                        print(f"Failed to create table {k} after {max_retries} attempts")
                    else:
                        time.sleep(1)  # Wait 1 second before retrying



    def get_all_destination_tables(self):
        query = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
        return [table[0] for table in self.destination_conn.read_data(query)]

    def get_last_updated_timestamp(self, table_name , schema = 'common'):
        sql = f"""
        SELECT max(last_updated) as last_updated FROM common.etl_metadata WHERE table_name = '{table_name}'
        """
        return self.destination_conn.read_data(sql)

    def get_source_data(self, schema, table_name, timestamp_key, last_updated_timestamp):
        query = f"SELECT * FROM {schema}.{table_name} WHERE cast({timestamp_key} as timestamp) > '{last_updated_timestamp}'"
        
        df = pd.read_sql(query, self.source_conn.engine)
        
        # drop dict type columns
        for col in df.columns:
            if '_at' in col:
                df[col] = pd.to_datetime(df[col], utc=True)
        
        for col in self.all_times:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], utc=True)
        # dynamic update to respective dtypes
        sales_all_columns = sales_etl_dtypes[table_name].keys()
        sales_dtypes = {k: v for k, v in sales_etl_dtypes[table_name].items() if v != 'datetime64[ns]'}
        sales_dtypes = fix_dtype_mapping_for_pandas(sales_dtypes)
        for cc in sales_all_columns:
            if cc not in df.columns:
                df[cc] = None
        df = df.astype(sales_dtypes)

        cols_to_drop = ['raw_app_meta_data','raw_user_meta_data']
        for col in cols_to_drop:
            if col in df.columns:
                df = df.drop(columns=[col], axis=1)
        logging.info(f"Source data fetched successfully for table: {table_name} Total records: {df.shape}")
        df = df[sales_all_columns]
        return df

    def create_temp_table(self, df, schema, table_name):
        logging.info(f"Creating temp table for table: {table_name}")
        temp_table_name = f"temp_{table_name}"
        self.destination_conn.write_data(f"DROP TABLE IF EXISTS {schema}.{temp_table_name};")
        df.to_sql(temp_table_name, self.destination_conn.engine, if_exists='replace', schema=schema, index=False)
        logging.info(f"Temp table created successfully for table: {table_name}")
        return True


    def upsert_data(self, source_schema, table_name, conflict_columns : list, df : pd.DataFrame):
        temp_table_name = f"temp_{table_name}"
        query = f"""
        INSERT INTO {source_schema}.{table_name} ({', '.join(df.columns)})
        SELECT * FROM common.{temp_table_name}
        ON CONFLICT ({', '.join(conflict_columns)})
        DO UPDATE SET
            {', '.join([f"{col} = EXCLUDED.{col}" for col in df.columns])};
        """
        self.destination_conn.write_data(query)
        logging.info(f"Data upserted successfully for table: {table_name}")
        logging.info(f"Dropping temp table for table: common.{table_name}")
        self.destination_conn.write_data(f"DROP TABLE IF EXISTS common.{temp_table_name};")
        logging.info(f"Temp table dropped successfully for table: {table_name}")
        
        return True
    
    def insert_metadata(self, table_name, last_updated):
        logging.info(f"Inserting metadata for table: {table_name}")
        sql = f"""
        INSERT INTO common.etl_metadata (table_name, last_updated) 
        VALUES ('{table_name}', '{last_updated}')
        """
        self.destination_conn.write_data(sql)
        logging.info(f"Metadata inserted successfully for table: {table_name}")
    
    def sync_data(self, source_schema, target_schema, table_name, timestamp_key, conflict_columns : list):
        '''
        This function is used to sync the data from the source database to the destination database.
        '''
        last_updated = self.get_last_updated_timestamp(f'{target_schema}.{table_name}')
        last_updated_timestamp = last_updated[0].get('last_updated')
        if last_updated_timestamp:
            logging.info(f"Last updated time for packaging: {last_updated}")
        else:
            last_updated_timestamp = datetime.strptime('1900-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')

        logging.info(f"Last updated timestamp for table: {target_schema}.{table_name} is {last_updated_timestamp}")
        
        logging.info(f"Fetching data for table: {table_name}")
        source_data = self.get_source_data(source_schema, table_name, timestamp_key, last_updated_timestamp)
        if source_data.empty:
            logging.info(f"No new data found for table: {table_name}")
            return False
        self.create_temp_table(source_data,'common', table_name)
        
        logging.info(f"Upserting data for table: {table_name}")
        self.upsert_data(target_schema, table_name, conflict_columns, source_data)
        logging.info(f"Data upserted successfully for table: {table_name}")
        return True
    
    def sync_auth_tables(self):
        '''
        This function is used to sync the auth tables from the source database to the destination database.
        '''
        logging.info("Syncing auth tables")
        table = 'users'
        if not self.sync_data('auth','sales', table, 'updated_at', ['id']):
            logging.info("No new data found for auth tables")
            return False

        self.insert_metadata('sales.'+table, datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S'))
        logging.info("Auth tables synced successfully")
        return True
    
    def sync_all_tables(self):
        '''
        This function is used to sync all the tables from the source database to the destination database.
        '''
        logging.info("Syncing all tables")

        for table, timestamp_key in self.timestamp_keys.items():
            if table in self.exculed_columns:
                continue
            current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"Syncing table: {table}")
            is_data_available = self.sync_data('public','sales', table, timestamp_key, ['id'])
            if not is_data_available:
                continue
            self.insert_metadata('sales.'+table, current_time)
            logging.info(f"Table: {table} synced successfully")
        
        logging.info("All tables synced successfully")

        return True

    def sync_user_roles(self):
        logging.info("Syncing user roles")
        table = 'user_roles'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for user roles")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("User roles synced successfully")
        return True
    
    def sync_customer(self):
        logging.info("Syncing customer")
        table = 'customer'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'modified_at', ['id']):
            logging.info("No new data found for customer")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Customer synced successfully")
        return True
    
    def sync_customer_quotations(self):
        logging.info("Syncing customer quotations")
        table = 'customer_quotations'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'updated_at', ['id']):
            logging.info("No new data found for customer quotations")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Customer quotations synced successfully")
        return True
    
    def sync_enquiries(self):
        logging.info("Syncing enquiries")
        table = 'enquiries'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'last_status_change', ['id']):
            logging.info("No new data found for enquiries")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Enquiries synced successfully")
        return True
    
    def sync_enquiry_clarifications(self):
        logging.info("Syncing enquiry clarifications")
        table = 'enquiry_clarifications'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry clarifications")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Enquiry clarifications synced successfully")
        return True
    
    def sync_enquiry_documents(self):
        logging.info("Syncing enquiry documents")
        table = 'enquiry_documents'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry documents")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Enquiry documents synced successfully")
        return True
    
    def sync_enquiry_status_history(self):
        logging.info("Syncing enquiry status history")
        table = 'enquiry_status_history'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry status history")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Enquiry status history synced successfully")
        return True
    
    def sync_enquiry_quotations(self):
        logging.info("Syncing enquiry quotations")
        table = 'enquiry_quotations'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for enquiry quotations")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Enquiry quotations synced successfully")
        return True
    
    def sync_sample_requests(self):
        logging.info("Syncing sample requests")
        table = 'sample_requests'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'last_status_change', ['id']):
            logging.info("No new data found for sample requests")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Sample requests synced successfully")
        return True
    
    def sync_quotation(self):
        logging.info("Syncing quotation")
        table = 'quotation'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'generated_at', ['id']):
            logging.info("No new data found for quotation")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quotation synced successfully")
        return True
    
    def sync_quotation_feedback(self):
        logging.info("Syncing quotation feedback")
        table = 'quotation_feedback'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for quotation feedback")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quotation feedback synced successfully")
        return True
    
    def sync_quote_generation_details(self):
        logging.info("Syncing quote generation details")
        table = 'quote_generation_details'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'generated_at', ['id']):
            logging.info("No new data found for quote generation details")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quote generation details synced successfully")
        return True
    
    def sync_purchase_orders(self):
        logging.info("Syncing purchase orders")
        table = 'purchase_orders'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for purchase orders")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Purchase orders synced successfully")
        return True
    
    def sync_sample_requests_attachments(self):
        logging.info("Syncing sample requests attachments")
        table = 'sample_requests_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for sample requests attachments")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Sample requests attachments synced successfully")
        return True
    
    def sync_sample_feedback(self):
        logging.info("Syncing sample feedback")
        table = 'sample_feedback'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for sample feedback")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Sample feedback synced successfully")
        return True
    
    def sync_sample_status_history(self):
        logging.info("Syncing sample status history")
        table = 'sample_status_history'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'changed_at', ['id']):    
            logging.info("No new data found for sample status history")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Sample status history synced successfully")
        return True
    
    def sync_quotation_feedback_attachments(self):
        logging.info("Syncing quotation feedback attachments")
        table = 'quotation_feedback_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for quotation feedback attachments")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quotation feedback attachments synced successfully")
        return True
    
    def sync_sample_feedback_attachments(self):
        logging.info("Syncing sample feedback attachments")
        table = 'sample_feedback_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for sample feedback attachments")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Sample feedback attachments synced successfully")
        return True
    
    def sync_quote_generation_options(self):
        logging.info("Syncing quote generation options")
        table = 'quote_generation_options'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'updated_at', ['id']):
            logging.info("No new data found for quote generation options")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quote generation options synced successfully")
        return True
    
    def sync_quote_generation_attachments(self):
        logging.info("Syncing quote generation attachments")
        table = 'quote_generation_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for quote generation attachments")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Quote generation attachments synced successfully")
        return True
    
    def sync_purchase_order_attachments(self):
        logging.info("Syncing purchase order attachments")
        table = 'purchase_order_attachments'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for purchase order attachments")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Purchase order attachments synced successfully")
        return True
    
    def sync_account_planning(self):
        logging.info("Syncing purchase order attachments")
        table = 'account_planning'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        
        if not self.sync_data('public','sales', table, 'updated_at', ['id']):
            logging.info("No new data found for account planning")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Account planning synced successfully")
        return True
    
    def sync_account_planning_history(self):
        logging.info("Syncing purchase order attachments")
        table = 'account_planning_history'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        logging.info(f"Current time: {current_time}")
        if not self.sync_data('public','sales', table, 'created_at', ['id']):
            logging.info("No new data found for account planning history")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Account planning history synced successfully")
        return True

    def sync_customer_meetings(self):
        logging.info("Syncing customer meetings")
        table = 'customer_meeting'
        current_time = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        if not self.sync_data('public','sales', table, 'updated_at', ['id']):
            logging.info("No new data found for customer meetings")
            return False
        self.insert_metadata('sales.'+table, current_time)
        logging.info("Customer meetings synced successfully")
        return True




