# Airflow Repository

This repository contains a setup for Apache Airflow with Docker Compose, including example DAGs, Database repo and script for task execution.
Add code file to the folder dags , it will automatically sync and upload the files to AWS S3 bucket “mstack-airflow-dag“ and Amazon airflow will pick the dags from the s3 bucket.

## Prerequisites
- Docker
- Docker Compose

## Repository Structure

```
mstack-airflow/
├── dags/
│   ├── hello_airflow.py
│   ├── enquiry_status_sync.py
│   └── config/
│       └── db_config.py
│       └── default.py
│   └── db_repo/
│       └── mongo_operations.py
│       └── postgress_operations.py
├── Dockerfile
├── docker-compose.yml
├── entrypoint.sh
├── requirements.txt
└── README.md
```

## Setup Instructions

1. **Clone the Repository**

   ```bash
   git clone <repository_url>
   cd mstack-airflow
   ```

2. **Build the image:**
   ```sh
   chmod +x entrypoint.sh
   docker-compose build --no-cache
   ```

3. **Start Airflow:**
   ```sh
   docker-compose up
   ```

4. **Access Airflow UI:**
   - Open [http://localhost:8080](http://localhost:8080)
   - Login: `admin` / `admin`

## Running the DAGs

1. **Add Your DAGs**

   Ensure your DAG files are located in the `dags/` directory.

2. **Trigger a DAG**

   To manually trigger a DAG:
   - Go to the Airflow UI.
   - Navigate to the "DAGs" tab.
   - Turn on the DAG toggle for the DAG you want to trigger.
   - Click the "Trigger DAG" button next to the DAG.

3. **Monitor DAG Runs**

   Monitor the progress and status of your DAG runs in the Airflow UI. Logs for each task can be viewed by clicking on the task instance.

## Troubleshooting

### Logs

Check the logs for the webserver, scheduler, and other services if you encounter issues:

```bash
docker-compose logs webserver
docker-compose logs scheduler
```

## Conclusion

This setup provides a basic environment for running and managing Airflow DAGs using Docker Compose. Customize the `dags/` directory and other configurations as needed for your use case. For more information on Airflow, visit the [Apache Airflow Documentation](https://airflow.apache.org/docs/apache-airflow/stable/).
