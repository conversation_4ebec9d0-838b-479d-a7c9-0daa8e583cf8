from airflow.models import Variable
from typing import Dict, Any
import os

class SupabaseConfig:

    STORAGE_BUCKETS = {'SALES_STACK': {
        "ENQUIRY_DOCUMENTS": 'enquiry_documents',
        "QUOTATION_DOCUMENTS": 'quotation-documents',
        "PURCHASE_ORDER_DOCUMENTS": 'purchase_order_documents',
        "SAMPLE_DOCUMENTS": 'sample-documents'
    }, 'PROCURE_STACK': {
        "ENQUIRY_DOCUMENTS": 'enquiry-documents',
        "QUOTATION_DOCUMENTS": 'quotation-documents',
        "PURCHASE_ORDER_DOCUMENTS": 'purchase-order-documents',
        "SAMPLE_DOCUMENTS": 'sample-documents'

    }}


    @staticmethod
    def get_sales_stack_supabase_params() -> Dict[str, Any]:
        """Get Supabase parameters for SalesStack"""
        return {
            'url': Variable.get('sales_supabase_url', default_var=os.getenv('SALES_SUPABASE_URL')),
            'key': Variable.get('sales_supabase_key', default_var=os.getenv('SALES_SUPABASE_KEY'))
        }


    @staticmethod
    def get_procure_stack_supabase_params() -> Dict[str, Any]:
        """Get Supabase parameters for ProcureStack"""

        return {
            'url': Variable.get('procure_supabase_url', default_var=os.getenv('PROCURE_SUPABASE_URL')),
            'key': Variable.get('procure_supabase_key', default_var=os.getenv('PROCURE_SUPABASE_KEY'))
        }
