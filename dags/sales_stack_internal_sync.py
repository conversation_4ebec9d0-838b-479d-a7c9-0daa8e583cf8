from datetime import datetime, timedelta, timezone
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import  DagRun
from airflow.utils.db import provide_session
from airflow.models import XCom
import logging
from typing import List, Dict, Any, Optional
from config.db_config import DatabaseConfig
from db.postgres_operations import PostgresOperations
import uuid
import json
import ast

from datetime import datetime, timezone
from airflow.utils.state import State
from sqlalchemy import desc

# Constants
DAG_ID = 'sales_stack_internal_sync'
MIN_STALE_DAYS_THRESHOLD = 30
MAX_STALE_DAYS_THRESHOLD = 32
VARIABLE_NAME = f"{DAG_ID}_last_run"

def get_last_successful_run(dag_id: str) -> datetime:
    """
    Get the last successful run datetime of a DAG.
    If no successful run is found, returns January 1st, 1900 in UTC.

    Args:
        dag_id (str): The ID of the DAG to check

    Returns:
        datetime: The datetime of the last successful run or January 1st, 1900 if no successful run found
    """
    @provide_session
    def _get_last_successful_run(dag_id, session=None):
        # Query the last successful run directly from DagRun model
        last_successful_run = session.query(DagRun).filter(
            DagRun.dag_id == dag_id,
            DagRun.state == State.SUCCESS
        ).order_by(desc(DagRun.execution_date)).first()
        logging.info(f"Last successful run: {last_successful_run}")
        if last_successful_run:
            return datetime(1900, 1, 1, tzinfo=timezone.utc)
            # Convert naive datetime to UTC timezone-aware datetime
            return last_successful_run.execution_date.replace(tzinfo=timezone.utc)
        
        # If no successful run found, return January 1st, 1900 in UTC
        return datetime(1900, 1, 1, tzinfo=timezone.utc)
    
    return _get_last_successful_run(dag_id)


def fetch_stale_quotations(**kwargs) -> List[Dict[str, Any]]:
    """
    Fetch the latest pricing_quotation_generated entries from enquiry_status_history
    and identify those between 30 and 32 days old
    """
    try:
        # Get the last successful run timestamp
        last_run_time = get_last_successful_run(DAG_ID)
        logging.info(f"Fetching quotations created after: {last_run_time}")
        
        # Connect to sales database
        postgres_params = DatabaseConfig.get_sales_stack_postgres_params()    
        sales_db = PostgresOperations(postgres_params)    
        with open('dags/sql/sales/fetch_quotations.sql', 'r') as file:
                query = file.read()
    
        # Calculate the stale threshold date (30 days ago)
        stale_threshold = (datetime.now(timezone.utc) - timedelta(days=MIN_STALE_DAYS_THRESHOLD)).strftime('%Y-%m-%d %H:%M:%S')
        max_stale_threshold = (datetime.now(timezone.utc) - timedelta(days=MAX_STALE_DAYS_THRESHOLD)).strftime('%Y-%m-%d %H:%M:%S')
        
        # Convert last_run_time to string if it's a datetime object
        if isinstance(last_run_time, datetime):
            last_run_time = last_run_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Execute the query with named parameters
        params = {
            "last_run_time": last_run_time,
            "stale_threshold": stale_threshold,
            "max_stale_threshold": max_stale_threshold
        }
        db_results = sales_db.read_data(query, params)
        
        if not db_results:
            logging.info("No stale quotations found")
            return []
        
        # Convert SQLAlchemy RowMapping objects to regular dictionaries
        serializable_results = []
        for row in db_results:
            # Convert each row to a dictionary and handle UUID and datetime objects
            row_dict = {}
            for key, value in dict(row).items():  # Convert RowMapping to dict first
                # Convert UUID objects to strings
                if isinstance(value, uuid.UUID):
                    row_dict[key] = str(value)
                # Convert datetime objects to ISO format strings
                elif isinstance(value, datetime):
                    row_dict[key] = value.isoformat()
                else:
                    row_dict[key] = value
            serializable_results.append(row_dict)
        
        logging.info(f"Found {len(serializable_results)} stale quotations")
        
        return serializable_results
    
    except Exception as e:
        logging.error(f"Error fetching stale quotations: {str(e)}")
        raise
    finally:
        if 'sales_db' in locals():
            sales_db.close()


def update_stale_quotations_status(stale_quotations: List[Dict[str, Any]], **kwargs) -> None:
    """
    Update the status of stale quotations to "Enquiry Lost" in the account_history table
    
    Args:
        stale_quotations: List of stale quotation records
    """
    if not stale_quotations:
        logging.info("No stale quotations to update")
        return
    
    # If stale_quotations is a string (from XCom), parse it
    if isinstance(stale_quotations, str):
        try:
            # First try to parse as JSON
            stale_quotations = json.loads(stale_quotations)
        except json.JSONDecodeError:
            try:
                # If JSON parsing fails, try to parse as a Python literal
                stale_quotations = ast.literal_eval(stale_quotations)
            except (SyntaxError, ValueError):
                logging.error(f"Failed to parse stale_quotations: {stale_quotations}")
                return
    
    logging.info(f"Updating {len(stale_quotations)} stale quotations to 'Enquiry Lost'")
    
    try:
        # Connect to sales database
        postgres_params = DatabaseConfig.get_sales_stack_postgres_params()
        sales_db = PostgresOperations(postgres_params)
        
        updated_count = 0
        
        for quotation in stale_quotations:
            try:
                customer_id = quotation['customer_id']
                chemical_name = quotation['chemical_name']
                
                # Update account_planning status to "Enquiry Lost"
                update_query = """
                UPDATE public.account_planning
                SET status = 'Enquiry Lost',
                    updated_at = CURRENT_TIMESTAMP
                WHERE customer_id = :customer_id
                AND chemical_name = :chemical_name
                """
                
                result = sales_db.write_data(update_query, {
                    "customer_id": customer_id,
                    "chemical_name": chemical_name
                })
                logging.info(f"Updated account_history for customer_id: {customer_id}, chemical_name: {chemical_name}")
                
                updated_count += 1
                
            except Exception as e:
                # Use safer dictionary access with .get() method
                enquiry_id = quotation.get('enquiry_id', 'unknown')
                logging.error(f"Error updating status for enquiry {enquiry_id}: {str(e)}")
                continue
        
        logging.info(f"Updated {updated_count} of {len(stale_quotations)} stale quotations to 'Enquiry Lost'")
        
    except Exception as e:
        logging.error(f"Error in update_stale_quotations_status: {str(e)}")
        raise
    finally:
        if 'sales_db' in locals():
            sales_db.close()


# Create the DAG
dag = DAG(
    DAG_ID,
     default_args={
        'owner': 'airflow',
        'depends_on_past': False,
        'email_on_failure': False,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5),
    },
    description='Monitor and identify stale quotations in the sales system',
    schedule=timedelta(days=1),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['monitoring', 'sales', 'quotations'],
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
)

# Task 1: Fetch stale quotations
fetch_task = PythonOperator(
    task_id='fetch_stale_quotations',
    python_callable=fetch_stale_quotations,
    
    dag=dag,
)

# Task 2: Update stale quotations status
update_task = PythonOperator(
    task_id='update_stale_quotations_status',
    python_callable=update_stale_quotations_status,
    op_kwargs={'stale_quotations': "{{ task_instance.xcom_pull(task_ids='fetch_stale_quotations') }}"},
    
    dag=dag,
)

# Set task dependencies
fetch_task >> update_task

