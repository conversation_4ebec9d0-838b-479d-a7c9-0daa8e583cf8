import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from typing import Dict, Any, Optional
import boto3
import tempfile
import os
from botocore.exceptions import ClientError

class S3Operations:
    def __init__(self, params: Dict[str, Any]):
        """Initialize S3 client with credentials and region"""
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=params['aws_access_key_id'],
            aws_secret_access_key=params['aws_secret_access_key'],
            region_name=params.get('region_name', 'us-east-1')
        )
        self.bucket_name = params['bucket_name']

    def download_file(self, file_path: str) -> Optional[str]:
        """Download a file from S3 and return the local file path"""
        try:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # Download the file
            self.s3_client.download_file(
                self.bucket_name,
                file_path,
                temp_path
            )
            
            return temp_path
        except ClientError as e:
            logging.error(f"Error downloading file {file_path} from S3: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error downloading file {file_path}: {str(e)}")
            return None

    def upload_file(self, local_file_path: str, destination_path: str, content_type: str) -> bool:
        """Upload a file to S3"""
        try:
            self.s3_client.upload_file(
                local_file_path,
                self.bucket_name,
                destination_path,
                ExtraArgs={'ContentType': content_type}
            )
            return True
        except ClientError as e:
            logging.error(f"Error uploading file to S3 {destination_path}: {str(e)}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error uploading file to {destination_path}: {str(e)}")
            return False

    def cleanup_temp_file(self, file_path: str) -> None:
        """Clean up temporary file"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logging.error(f"Error cleaning up temporary file {file_path}: {str(e)}") 