from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional
from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from contextlib import contextmanager
import logging
import uuid

import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS, QueryRun

class ProductSyncManager:
    """Manager class for syncing chemicals from ProcureStack to MongoDB products"""

    def __init__(self):
        self.mongo_ops: Optional[MongoOperations] = None
        self.procure_db: Optional[PostgresOperations] = None

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            # Connect to MongoDB
            mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
            self.mongo_ops = MongoOperations(mongo_conn_string, mongo_db)

            # Connect to ProcureStack PostgreSQL
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            self.procure_db = PostgresOperations(procure_params)

            logging.info("Connected to MongoDB and PostgreSQL databases")
            yield

        finally:
            if self.mongo_ops:
                self.mongo_ops.close()
            if self.procure_db:
                self.procure_db.close()
            logging.info("Database connections closed")

    def generate_next_product_id(self) -> str:
        """Generate next product ID in format PR00001"""
        # all_products = self.mongo_ops.read_data(
        #     collection_name="product",
        #     query={},
        #     projection={"productId": 1}
        # )
        
        # max_num = 0
        # for product in all_products:
        #     product_id = product.get('productId', '')
        #     if product_id and product_id.startswith('PR'):
        #         try:
        #             num = int(product_id[2:])
        #             max_num = max(max_num, num)
        #         except ValueError:
        #             continue
        
        # next_num = max_num + 1
        # return f"PR{next_num:05d}"
        result = self.mongo_ops.get_collection("product").aggregate([
          {
            "$match": {
                "productId": {"$regex": "^PR\\d+$"}  # Match PR followed by numbers
            }
           },
            {
            "$project": {
                "num": {"$toInt": {"$substr": ["$productId", 2, -1]}}
            }
            },
         {
            "$group": {
                "_id": None,
                "maxNum": {"$max": "$num"}
            }
        }
       ])
    
        max_num = next(result, {"maxNum": 0})["maxNum"]
        return f"PR{max_num + 1:05d}"

    def transform_chemical_to_product(self, chemical: Dict[str, Any]) -> Dict[str, Any]:
        """Transform chemical data to product schema"""
        # Convert categories to required format
        categories = []
        if chemical.get('category'):
            categories.append({
                "category": chemical['category'],
                "subCategory": chemical['sub_category']
            })

        # Convert UUID to string for createdBy and lastUpdatedBy
        created_by = chemical.get('created_by')
        if isinstance(created_by, uuid.UUID):
            created_by = str(created_by)

        last_updated_by = chemical.get('updated_by')
        if isinstance(last_updated_by, uuid.UUID):
            last_updated_by = str(last_updated_by)

        return {
            "productId": chemical.get('product_id'),
            "tradeName": chemical.get('chemical_name'),
            "grade": chemical.get('grade'),
            "technicalName": chemical.get('technical_name'),
            "synonyms": chemical.get('synonyms', []),
            "categories": categories,
            "functions": [chemical.get('functions')],
            "family": [chemical.get('product_family', '')],
            "endUses": chemical.get('end_uses', []),
            "features": chemical.get('features', []),
            "casNumber": chemical.get('cas_number'),
            "createdBy": created_by,
            "lastUpdatedBy": last_updated_by,
            "createdAt": chemical.get('created_at'),
            "lastUpdatedAt": chemical.get('updated_at')
        }

    def sync_products(self) -> bool:
        """Sync chemicals from ProcureStack to MongoDB products"""
        try:
            # Fetch chemicals modified since last sync
            select_query = """
                SELECT 
                    c.*,
                    array_agg(DISTINCT cs.synonym) as synonyms
                FROM chemicals c
                LEFT JOIN chemical_synonyms cs ON c.id = cs.chemical_id                                   
                WHERE c.updated_at >= NOW() - INTERVAL '5 minutes'
                    OR c.created_at >= NOW() - INTERVAL '5 minutes'
                GROUP BY c.id
            """
            
            chemicals = self.procure_db.read_data(select_query, {"execution_time": QueryRun.strftime('%Y-%m-%d %H:%M:%S.%f%z')})
            logging.info(f"Executing query with execution_time: {QueryRun.strftime('%Y-%m-%d %H:%M:%S.%f%z')}")
            if not chemicals:
                logging.info("No chemicals found to sync")
                return True
                
            logging.info(f"Found {len(chemicals)} chemicals to sync")
            
            success_count = 0
            for chemical in chemicals:
                try:
                    # Transform chemical to product format
                    mongo_product = self.transform_chemical_to_product(chemical)
                    
                    # Check if product exists
                    existing_product = self.mongo_ops.read_data(
                        collection_name="product",
                        query={"productId": mongo_product["productId"]}
                    ) if mongo_product["productId"] else []

                    if existing_product:
                        # Update existing product
                        result = self.mongo_ops.update_data(
                            collection_name="product",
                            query={"productId": mongo_product["productId"]},
                            update={"$set": mongo_product}
                        )
                        logging.info(f"Updated existing product: {mongo_product['productId']}")
                    else:
                        # Generate new product ID
                        new_product_id = self.generate_next_product_id()
                        mongo_product["productId"] = new_product_id
                        logging.info(f"Generated new product ID: {new_product_id}")
                        logging.info(f"New product: {mongo_product}")

                        # Insert new product
                        result = self.mongo_ops.write_data(
                            collection_name="product",
                            data=mongo_product
                        )
                        
                        logging.info(f"Inserted new product: {result}")
                        
                        # Convert UUID to string before using in update query
                        chemical_id = str(chemical["id"]) if isinstance(chemical["id"], uuid.UUID) else chemical["id"]
                        
                        # Update PostgreSQL with the new product_id
                        update_query = """
                            UPDATE chemicals 
                            SET product_id = :product_id 
                            WHERE id = :id
                        """
                        self.procure_db.update_data(
                            update_query, 
                            {
                                "product_id": new_product_id,
                                "id": chemical_id  # Now using string version of UUID
                            }
                        )
                        
                        logging.info(f"Created new product with ID: {new_product_id}")
                    
                    success_count += 1
                    
                except Exception as e:
                    logging.error(f"Error syncing chemical {chemical.get('id')}: {str(e)}")
                    continue
            
            logging.info(f"Successfully synced {success_count}/{len(chemicals)} products")
            return True
            
        except Exception as e:
            logging.error(f"Error in product sync: {str(e)}")
            raise

def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""
    def wrapper():
        sync_manager = ProductSyncManager()
        try:
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")
        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            raise
    return wrapper

@create_sync_task
def sync_products(manager: ProductSyncManager) -> bool:
    """Sync products from ProcureStack to MongoDB"""
    return manager.sync_products()

# Create the DAG
dag = DAG(
    'procure_to_mongo_products_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
    },
    description='Sync chemicals from ProcureStack to MongoDB products',
    schedule='*/5 * * * *',  # Run every 5 minutes
    # or alternatively: schedule=timedelta(days=1),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'mongodb', 'postgres', 'products', 'chemicals'],
    dagrun_timeout=timedelta(minutes=10),
    max_active_runs=1,
)

# Create task
sync_task = PythonOperator(
    task_id='sync_products',
    python_callable=sync_products,
    dag=dag,
    execution_timeout=timedelta(minutes=5),
)
