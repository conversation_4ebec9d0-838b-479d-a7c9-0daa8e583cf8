import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager
from airflow import DAG
from airflow.operators.python import PythonOperator
from config.default import DEFAULT_DAG_ARGS
from config.db_config import DatabaseConfig
from config.default import QueryRun
from db.postgres_operations import PostgresOperations
from db.mongo_operations import MongoOperations
from config.status_mapping import create_status_mapping
from failure_task import send_slack_alert
from exception_task_email_dag import send_exception_slack_alert

# Constants
BATCH_SIZE = 100
MAX_RETRIES = 0
RETRY_DELAY = timedelta(minutes=5)

class LogiProcureStatusSyncManager:
    """Manager class for syncing status from LogiStack to ProcureStack"""

    def __init__(self):
        self.mongo_ops: Optional[MongoOperations] = None
        self.procure_db: Optional[PostgresOperations] = None

        # Initialize status mapping by fetching from config
        self.status_mapping = create_status_mapping()['LogiStack_to_ProcureStack']
        self.valid_status_names = list(self.status_mapping.keys())

    @contextmanager
    def database_connections(self):
        """Context manager for database connections"""
        try:
            # Connect to MongoDB using MongoOperations
            mongo_conn_string, mongo_db = DatabaseConfig.get_logi_stack_mongo_params()
            self.mongo_ops = MongoOperations(mongo_conn_string, mongo_db)

            # Connect to ProcureStack PostgresSQL
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            self.procure_db = PostgresOperations(procure_params)

            logging.info("Connected to MongoDB and PostgresSQL databases")
            yield

        finally:
            if self.mongo_ops:
                self.mongo_ops.close()
            logging.info("Database connections closed")



    def fetch_completed_sample_orders(self) -> List[Dict[str, Any]]:
        """Fetch completed sample orders from LogiStack, ordered by secondaryId asc and taskCompletedOn desc.
        Only includes orders where secondaryId ends with '_WH'.
        This ensures we get the latest status for each enquiry first."""
        logging.info(f"Fetching completed sample orders from {QueryRun} to now with secondaryId ending with '_WH'")

        query = {
            "orderType": "SAMPLE",
            "status": "COMPLETED",
            "taskCompletedOn": {"$gte": QueryRun},
            "name": {"$in": self.valid_status_names},  # Add filter for valid status names
            "secondaryId": {"$not": {"$regex": "_WH$"}},  # Not include secondaryId ending with _WH
            "$or": [
                {"is_synced": False},
                {"is_synced": {"$exists": False}}
            ]  # Only fetch records where is_synced is false or not present
        }
        logging.info(f"MongoDB query: {query}")

        projection = {
            "is_synced":1,
            "entityType": 1,
            "entityId": 1,
            "orderId": 1,
            "secondaryId": 1,  # first 5 digits of enquiry_id
            "name": 1,         # LogiStack status name
            "status": 1,
            "taskCompletedOn": 1,
            "_id": 1
        }

        # Sort by secondaryId ascending and taskCompletedOn descending
        # This ensures we get the latest status for each enquiry first
        orders = self.mongo_ops.read_data(
            "activity",
            query=query,
            projection=projection,
            sort=[("secondaryId", 1), ("taskCompletedOn", 1)]
        )
        logging.info(f"Found {len(orders)} completed sample orders with valid status names")
        if orders:
            logging.info(f"Sample order data: {orders[:2]}")  # Log first 2 orders as sample
        return orders

    def enrich_orders_with_enquiry_data(self, orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enrich orders with enquiry data and filter out orders without matching enquiries.

        Args:
            orders: List of orders from LogiStack

        Returns:
            List of orders enriched with enquiry data, filtered to only include orders with matching enquiries
        """
        if not orders:
            logging.info("No orders to enrich")
            return []
            
        enriched_orders = []
        for order in orders:
            try:
                # Extract the substring after '-' from secondaryId (e.g., "e3942" from "SMP-e3942")
                secondary_id = order.get("secondaryId", "")

                # Query ProcureStack to find matching enquiry IDs
                # Looking for IDs where the first 5 characters match the extracted code
                query = """
                    SELECT id, status, enquiry_id FROM sample_requests
                    WHERE id =:secondary_id
                    LIMIT 1
                """
                params = { "secondary_id": secondary_id }
                
                result = self.procure_db.read_data(query, params)
                
                if not result or len(result) == 0:
                    logging.warning(f"No matching enquiry found for order code {secondary_id}")
                    continue
                
                sample_id = result[0].get("id")
                enquiry_id = result[0].get("enquiry_id")
                status = result[0].get("status")
                
                # Enrich the order with the enquiry data
                enriched_order = order.copy()
                enriched_order["sample_id"] = sample_id
                enriched_order["enquiry_id"] = enquiry_id
                enriched_order["status"] = status
                
                logging.info(f"Matched order {secondary_id} with sample {sample_id}, current status: {status}")
                enriched_orders.append(enriched_order)
                
            except Exception as e:
                logging.error(f"Error enriching order {order.get('secondaryId', 'Unknown')}: {str(e)}", exc_info=True)
                continue
        
        logging.info(f"Enriched {len(enriched_orders)} of {len(orders)} orders with enquiry data")
        return enriched_orders

    def sync_status_to_procure(self, orders: List[Dict[str, Any]], **kwargs) -> bool:
        """Sync status changes to ProcureStack.
        Orders are already sorted by secondaryId asc and taskCompletedOn desc,
        so we only need to process the first occurrence of each secondaryId."""
        try:
            if not orders:
                logging.info("No completed sample orders found to sync")
                return True

            # Enrich orders with enquiry data
            enriched_orders = self.enrich_orders_with_enquiry_data(orders)
            if not enriched_orders:
                logging.info("No orders to process after enrichment")
                return True

            success_count = 0
            for order in enriched_orders:
                try:
                    logging.info(f"Processing Order details: {order}")

                    # Map LogiStack status to ProcureStack status and get validation rules
                    logi_status_name = order['name']
                    mapping_entry = self.status_mapping.get(logi_status_name)

                    if not mapping_entry:
                        logging.error(f"No mapping found for LogiStack status '{logi_status_name}' in status mapping. Skipping order {order['secondaryId']}")
                        continue

                    procure_status = mapping_entry['ProcureStack']
                    previous_valid_states = mapping_entry['PreviousValidStates']
                    logging.info(f"Mapping LogiStack status '{logi_status_name}' to ProcureStack status '{procure_status}'. Valid previous states: {previous_valid_states}")

                    # Check if ProcureStack status requires specific data fetching (MRD, delivery info)
                    if procure_status == "sample_ready":
                        # Get MRD date from LogiStack Using secondaryId
                        mrd_date = self.get_mrd_date(order["secondaryId"])
                        if mrd_date:
                            # Update MRD date in ProcureStack
                            if not self.update_mrd_to_procure(order["sample_id"], mrd_date):
                                logging.warning(f"Failed to update MRD date for enquiry {order['sample_id']}")

                    if procure_status == "in_transit_to_customer":
                        # Get delivery date, tracking number, and carrier name from LogiStack
                        delivery_date, tracking_number, carrier_name, tracking_url = self.get_delivery_date_tracking_no_carrier_name(order["secondaryId"])
                        if delivery_date:
                            # Update delivery date, tracking number, and carrier name in ProcureStack
                            if not self.update_delivery_date_tracking_no_carrier_name_to_procure(order["sample_id"], delivery_date, tracking_number, carrier_name, tracking_url):
                                logging.warning(f"Failed to update delivery info for sample {order['sample_id']}")



                    if order["status"] != procure_status:
                        self.update_sample_request(order, procure_status, previous_valid_states)
                    else:
                        logging.info(f"Sample {order['sample_id']} already has status {order['status']} no need to update to '{procure_status}' , skipping update")
                        success_count += 1
                        continue # Move to the next order if status already matches

                    self.insert_into_sample_status_history(order, procure_status)
                    #self.update_entry_synced(order)

                    success_count += 1
                    logging.info(f"Successfully processed order {order['secondaryId']}")

                except Exception as e:
                    logging.error(f"Error syncing order {order['secondaryId']}: {str(e)}", exc_info=True)
                    send_exception_slack_alert(e,context_info=kwargs)



            logging.info(f"Successfully synced {success_count}/{len(enriched_orders)} status changes")
            return True

        except Exception as e:
            logging.error(f"Error in sync_status_to_procure: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)

            return False

    def insert_into_sample_status_history(self, order: Dict[str, Any], procure_status: str, **kwargs) -> bool:
        try:
            # Insert into status_changes
            insert_query = """INSERT INTO sample_status_history (sample_request_id, sample_status, changed_at, enquiry_id)
                                                        VALUES (:sample_request_id, :sample_status, :created_at, :enquiry_id)
                              ON CONFLICT (sample_request_id, sample_status) DO NOTHING"""
            params = {
                "sample_request_id": order["sample_id"],
                "sample_status": procure_status,
                "created_at": order["taskCompletedOn"],
                "enquiry_id": order["enquiry_id"]
            }
            logging.info(f"Inserting status change record with params: {params}")

            self.procure_db.write_data(insert_query, params)
            logging.info(f"Successfully inserted status change record for enquiry {order['enquiry_id']}")
            return True

        except Exception as e:
            logging.error(f"Error inserting status change record for enquiry {order['enquiry_id']}: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)

            raise

    def update_sample_request(self, order: Dict[str, Any], procure_status, previous_valid_states, **kwargs) -> bool:
        try:
            logging.info(f"Updating enquiry {order['enquiry_id']} status from '{order['status']}' to '{procure_status}'")
            update_query = """UPDATE sample_requests
                              SET status = :new_status,
                                  last_status_change = CURRENT_TIMESTAMP
                              WHERE id = :sample_id and status = ANY(:previous_valid_states)"""
            update_params = {
                "new_status": procure_status,
                "sample_id": order["sample_id"],
                "previous_valid_states": previous_valid_states  # Use the specific valid states for this transition
            }
            self.procure_db.write_data(update_query, update_params)
            logging.info(f"Successfully updated enquiry {order['sample_id']} status from '{order['status']}' to '{procure_status}'")
            return True
        except Exception as e:
            logging.error(f"Error updating enquiry {order['sample_id']} status: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)

            raise

    def update_entry_synced(self, order: Dict[str, Any]) -> bool:
        # Update MongoDB activity collection to mark the order as synced
        # Get the MongoDB _id from the order
        mongo_id = order.get("_id")
        if mongo_id:
            # Update the activity document to set is_synced = true
            update_result = self.mongo_ops.update_one(
                collection_name="activity",
                query={"_id": mongo_id},
                update={"$set": {"is_synced": True}}
            )

            if update_result:
                logging.info(f"Successfully marked activity {mongo_id} as synced from MongoDB")
                return True

            logging.warning(f"Failed to update MongoDB activity {mongo_id}. Document may not exist or already synced.")
            return False
        else:
            logging.warning( f"No MongoDB _id found for order {order['secondaryId']}, cannot update sync status")
            raise

    def get_mrd_date(self, order_id: str, **kwargs) -> Optional[datetime]:
        """Get the MRD date for a given order ID from supplierOrderBook collection.

        Args:
            order_id: The orderBookId to search for

        Returns:
            Optional[datetime]: The MRD date if found, None otherwise
        """
        try:
            # First get entityId from activity collection
            activity_query = {
                "entityType": "SUPPLIER_ORDER_BOOK",
                "secondaryId": order_id
            }
            activity_projection = {"entityId": 1}

            activity_result = self.mongo_ops.read_data(
                "activity",
                query=activity_query,
                projection=activity_projection
            )

            if not activity_result or len(activity_result) == 0:
                logging.warning(f"No activity record found for order ID: {order_id}")
                return None

            entity_id = activity_result[0].get("entityId")
            logging.info(f"Found entityId: {entity_id} for order ID: {order_id}")

            # Then get MRD from supplierOrderBook using entityId
            query = {"orderBookId": entity_id}
            projection = {"mrd": 1}
            logging.info(f"Fetching MRD for entityId: {entity_id}")

            result = self.mongo_ops.read_data(
                "supplierOrderBook",
                query=query,
                projection=projection
            )

            if result and len(result) > 0 and "mrd" in result[0]:
                return result[0]["mrd"]

            logging.warning(f"No MRD found for entityId: {entity_id}")
            return None

        except Exception as e:
            logging.error(f"Error fetching MRD for order ID {order_id}: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)
            raise

    def update_mrd_to_procure(self, sample_id: str, mrd: datetime, **kwargs) -> bool:
        """Update the MRD date in sample_initiation_details column of enquiries.

        Args:
            enquiry_id: The ID of the enquiry to update
            mrd: The Material Required Date to set

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            if not mrd:
                logging.warning(f"No MRD date provided for sample {sample_id}, skipping update")
                return False

            update_query = """
                UPDATE sample_requests
                SET mrd_date = :mrd_date
                WHERE id::text = :sample_id
            """

            params = {
                "sample_id": sample_id,
                "mrd_date": mrd
            }

            self.procure_db.write_data(update_query, params)
            logging.info(f"Successfully updated MRD date to {mrd} for sample {sample_id}")
            return True

        except Exception as e:
            logging.error(f"Error updating MRD date for sample {sample_id}: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)
            raise

    def get_delivery_date_tracking_no_carrier_name(self, order_id: str, **kwargs) -> Tuple[Optional[datetime], Optional[str], Optional[str], Optional[str]]:
        """Get the delivery date, tracking number, and carrier name for a given order ID from customerOrder collection.

        Args:
            order_id: The purchaseOrderNumber to search for

        Returns:
            Tuple[Optional[datetime], Optional[str], Optional[str]]: A tuple containing:
                - delivery_date: The sampleDispatchedOn date if found, None otherwise
                - tracking_number: The tracking number if found, None otherwise
                - carrier_name: The carrier name if found, None otherwise
        """
        try:
            query = {"purchaseOrderNumber": order_id}
            projection = {
                "sampleDispatchedOn": 1,
                "trackingNumber": 1,
                "carrierName": 1,
                "trackingUrl": 1
            }

            result = self.mongo_ops.read_data(
                "customerOrder",
                query=query,
                projection=projection
            )

            if result and len(result) > 0:
                delivery_date = result[0].get("sampleDispatchedOn")
                tracking_number = result[0].get("trackingNumber")
                carrier_name = result[0].get("carrierName")
                tracking_url = result[0].get("trackingUrl")

                logging.info(f"Found delivery info for order {order_id}: "
                           f"delivery_date={delivery_date}, "
                           f"tracking_number={tracking_number}, "
                           f"carrier_name={carrier_name},"
                           f"tracking_url={tracking_url}")

                return delivery_date, tracking_number, carrier_name, tracking_url

            logging.warning(f"No delivery info found for order ID: {order_id}")
            return None, None, None, None

        except Exception as e:
            logging.error(f"Error fetching delivery info for order ID {order_id}: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)
            raise

    def update_delivery_date_tracking_no_carrier_name_to_procure(self, sample_id: str, delivery_date: datetime,
                                                                 tracking_number: str, carrier_name: str, tracking_url: str, **kwargs) -> bool:
        """Update the delivery date, tracking number, and carrier name, tracking_url in sample_initiation_details table.

        Args:
            enquiry_id: The ID of the enquiry to update
            delivery_date: The expected delivery date
            tracking_number: The tracking number
            carrier_name: The carrier name
            tracking_url: The tracking url

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            if not delivery_date:
                logging.warning(f"No delivery date provided for enquiry {sample_id}, skipping update")
                return False

            update_query = """
                UPDATE sample_requests
                SET tracking_number = :tracking_number,
                    carrier_name = :carrier_name,
                    expected_delivery_date = :delivery_date,
                    tracking_url = :tracking_url
                WHERE id = :sample_id
            """

            params = {
                "sample_id": sample_id,
                "delivery_date": delivery_date,
                "tracking_number": tracking_number if tracking_number else None,
                "tracking_url": tracking_url if tracking_url else None,
                "carrier_name": carrier_name if carrier_name else None
            }

            self.procure_db.write_data(update_query, params)
            logging.info(f"Successfully updated delivery info for sample_id {sample_id}: "
                        f"delivery_date={delivery_date}, "
                        f"tracking_number={tracking_number}, "
                         f"tracking_url={tracking_url}, "
                        f"carrier_name={carrier_name}")
            return True

        except Exception as e:
            logging.error(f"Error updating delivery info for sample_id {sample_id}: {str(e)}", exc_info=True)
            send_exception_slack_alert(e, context_info=kwargs)
            raise


def create_sync_task(func: callable) -> callable:
    """Create a sync task with proper error handling and logging"""
    def wrapper(**kwargs):
        sync_manager = LogiProcureStatusSyncManager()
        try:
            with sync_manager.database_connections():
                if not func(sync_manager):
                    raise Exception(f"Failed to execute {func.__name__}")
                logging.info(f"Successfully completed {func.__name__}")

        except Exception as e:
            logging.error(f"Task failed: {str(e)}")
            send_exception_slack_alert(e, context_info=kwargs)
            raise
    return wrapper


@create_sync_task
def sync_sample_delivery_status(manager: LogiProcureStatusSyncManager) -> bool:
    """Sync sample delivery status from LogiStack to ProcureStack"""
    completed_orders = manager.fetch_completed_sample_orders()
    return manager.sync_status_to_procure(completed_orders)



# Create the DAG
dag = DAG(
    'logi_to_procure_all_sync',
    default_args={
        **DEFAULT_DAG_ARGS,
        'on_failure_callback': send_slack_alert,
    },
    description='Sync completed sample delivery status from LogiStack to ProcureStack',
    schedule=timedelta(minutes=3),
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['database', 'sync', 'mongodb', 'postgres', 'samples'],
    dagrun_timeout=timedelta(minutes=10),  # Auto-fail after 30 minutes
    max_active_runs=1,  # No concurrent runs
)

# Create task
sync_task = PythonOperator(
    task_id='sync_sample_delivery_status',
    python_callable=sync_sample_delivery_status,
    dag=dag,
    execution_timeout=timedelta(minutes=5),
)
