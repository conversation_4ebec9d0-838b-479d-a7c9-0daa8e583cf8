import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from config.db_config import DatabaseConfig
from dotenv import load_dotenv
import pandas as pd
import logging
import datetime
import sqlalchemy as ss
import random
from datetime import timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from config.default import DEFAULT_DAG_ARGS
from functools import partial
from datetime import datetime, timezone
print(ss.__version__, 'main_code')
from etl.mongo_etl import MongoETL

logging.basicConfig(level=logging.INFO)

load_dotenv()


dag = DAG(
    dag_id='mongo_to_reporting_dag',
    default_args={**DEFAULT_DAG_ARGS},
    description='Sync data from MongoDB to PostgreSQL',
    schedule='@hourly',  # Runs every 1 hour
    catchup=False,
    tags=['database', 'sync', 'mongodb', 'postgres'],
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
)

def sync_data(sync_function):
    """Generic sync function that creates MongoETL instance and calls specified sync method"""
    mongo_etl = MongoETL()
    getattr(mongo_etl, sync_function)()

# Define sync tasks configuration
sync_tasks = [
    ('create_all_tables', 'create_all_tables'),
    ('packaging', 'sync_packaging'),
    ('product', 'sync_product'),
    ('customers', 'sync_customers'),
    ('employee', 'sync_employees'),
    ('customer_orders', 'sync_customer_orders'),
    ('order_book', 'sync_order_book'),
    ('supplier', 'sync_supplier'),
    ('supplier_order_book', 'sync_supplier_order_book'),
    ('supplier_order', 'sync_supplier_order'),
    ('users', 'sync_users'),
    ('product_batch', 'sync_product_batch'),
    ('inventory', 'sync_inventory'),
    ('inventory_product_transaction', 'sync_inventory_product_transaction'),
    ('activity_log', 'sync_activity_log'),
    ('operational_invoices', 'sync_operational_invoices'),
]

# Create tasks dynamically
tasks = {}
for task_name, sync_function in sync_tasks:
    tasks[task_name] = PythonOperator(
        task_id=f'sync_{task_name}',
        python_callable=partial(sync_data, sync_function),
        dag=dag,
        execution_timeout=timedelta(minutes=5),
    )

# Set dependencies
for i in range(len(sync_tasks) - 1):
    tasks[sync_tasks[i][0]] >> tasks[sync_tasks[i + 1][0]]







