 CREATE TABLE IF NOT EXISTS logistics.packaging(
    packaging_uid VARCHAR(255),
    type VA<PERSON><PERSON><PERSON>(255),
    pack_size VARCHAR(255),
    tare_weight VARCHAR(255),
    uom VARCHAR(255),
    packaging_name VARCHAR(255),
    created_at TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(255),
    deleted BOOLEAN,
    p_size FLOAT,
    p_uom VARCHAR(255),
    t_weight FLOAT,
    t_weight_uom VARCHAR(255),
    updated_at TIMESTAMP,
    updated_by VARCHAR(255),

    PRIMARY KEY(packaging_uid)
);