<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .delayed-tasks {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-TODO {
            color: #ffc107;
            font-weight: bold;
        }
        .status-IN_PROGRESS {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-COMPLETED {
            color: #28a745;
            font-weight: bold;
        }
        .status-BLOCKED {
            color: #dc3545;
            font-weight: bold;
        }
        .group-tag {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .employee-info {
            background-color: #e7f5ff;
            padding: 8px;
            margin-bottom: 2px;
            border-radius: 4px;
        }
        .action-needed {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Your Delayed Tasks Report</h2>
        <p>Generated on: {{ generation_time }}</p>
    </div>
    
    <div class="action-needed">
        <h3>⚠️ Action Required</h3>
        <p>You have {{ delayed_tasks|length }} overdue task(s) that require your attention. Please review and update these tasks as soon as possible.</p>
    </div>
    
    <div class="delayed-tasks">
        <h3>Your Overdue Tasks</h3>
        <table>
            <thead>
                <tr>
                    <th>Task Name</th>
                    <th>Entity ID</th>
                    <th>Order ID</th>
                    <th>Status</th>
                    <th>Group</th>
                    <th>Due Date</th>
                </tr>
            </thead>
            <tbody>
                {% for task in delayed_tasks %}
                <tr>
                    <td>{{ task.entityType }}</td>
                    <td>{{ task.entityId }}</td>
                    <td>{{ task.orderId }}</td>
                    <td class="status-{{ task.status }}">{{ task.status }}</td>
                    <td><span class="group-tag">{{ task.group }}</span></td>
                    <td>{{ task.dueDate }}</td>
                </tr>
                {% endfor %}
                
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>If you have any questions, please contact your supervisor.</p>
    </div>
</body>
</html> 