# Standard library imports
import logging
import os
import re
from collections import defaultdict
from datetime import datetime, timezone, timedelta
from zoneinfo import ZoneInfo

# Third-party imports
import pandas as pd
import pytz
import pendulum
from dateutil import parser

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import Variable
from jinja2 import Environment, FileSystemLoader

# Local application imports
from config.action_mapping import create_action_mapping
from config.db_config import DatabaseConfig
from config.default import DEFAULT_DAG_ARGS
from config.workflow_mapping import create_workflow_mapping
from db.mongo_operations import MongoOperations
from db.postgres_operations import PostgresOperations
from ses_hook import SESHook
from config.logi_steps import steps, procure_stack_sla, sales_stack_sla, names_from, names_to
from config.location_mapping import location_mapping
from config.workflow_mapping import sample_flow_mapping
from config.owner_email_mapping import owner_to_bu_head

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

DEFAULT_TIMEZONES = {
    'India': 'Asia/Kolkata',
    'USA': 'America/New_York',
    'Canada': 'America/Toronto',
    'UAE': 'Asia/Dubai',
    'China': 'Asia/Shanghai'
}

# Constants
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
THRESHOLD_DATE = datetime(2025, 1, 15)
# DEFAULT_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] 
DEFAULT_EMAILS = ['<EMAIL>']


# Manager class
class EmailNotificationManager:
    """Manager class for handling email notifications related to SLA breaches."""

    def __init__(self, country='India'):
        self.sales_db = None
        self.procure_db = None
        self.logi_db = None
        self.country = country

        self.action_mapping = create_action_mapping()
        self.workflow_mapping = create_workflow_mapping()
        self.location_mapping = location_mapping()
        self._load_location_data() # Renamed for clarity

        self.template_env = Environment(
            loader=FileSystemLoader(TEMPLATE_DIR),
            autoescape=True
        )
        self.ses_hook = SESHook()
        self.aws_config = DatabaseConfig.get_aws_ses_params()
        self.sender_email = self.aws_config.get('sender_email')
        if not self.sender_email:
            logging.error("Sender email not configured in AWS SES parameters.")
            raise ValueError("Sender email is required for sending notifications.")

        self.insert_query = """
            INSERT INTO public.email_notification_history (email_type, document_ref_id, state, created_at)
            VALUES (:email_type, :document_ref_id, :state, NOW())
            ON CONFLICT (email_type, document_ref_id, state)
            DO UPDATE SET created_at = NOW()
        """

    def initialize_connections(self):
        """Initialize database connections for Sales, Procure, and Logi stacks."""
        try:
            sales_params = DatabaseConfig.get_sales_stack_postgres_params()
            procure_params = DatabaseConfig.get_procuro_stack_postgres_params()
            logi_params = DatabaseConfig.get_logi_stack_mongo_params()

            self.sales_db = PostgresOperations(sales_params)
            self.procure_db = PostgresOperations(procure_params)
            self.logi_db = MongoOperations(logi_params[0], logi_params[1])
            logging.info("Database connections initialized successfully.")
        except Exception as e:
            logging.error(f"Error initializing database connections: {e}")
            raise

    def _load_location_data(self):
        """Loads and processes location mapping data."""
        self.loc_map = {dt.get('email'): key for key, val in self.location_mapping.items() for dt in val}
        logging.info("Location mapping data loaded.")

    def get_logi_pipeline(self): # Renamed for clarity
        """Constructs and returns the MongoDB aggregation pipeline for Logi stack activities."""
        collection = self.logi_db.get_collection("activity")
        name_regex_list = [{"name": {"$regex": name, "$options": "i"}} for name in names_from + names_to]

        pipeline = [
    {
        "$match": {
            "lastUpdatedAt": {"$gt": THRESHOLD_DATE},
            "$or": name_regex_list,  # ensure this is a valid list
            "status": {"$in": ["COMPLETED", "TODO"]}
        }
    },
    {
        "$lookup": {
            "from": "email_notification_history",
            "let": {
                "order_id_str": "$_id",
                "activity_name": "$name",
                "today": { "$dateTrunc": { "date": "$$NOW", "unit": "day" } }
            },
            "pipeline": [
                {
                    "$match": {
                        "$expr": {
                            "$and": [
                                { "$eq": ["$email_type", "sla_breach"] },
                                { "$eq": ["$document_ref_id", { "$toString": "$$order_id_str" }] },
                                { "$eq": ["$state", "$$activity_name"] },
                                { "$gte": ["$created_at", "$$today"] },
                                {
                                    "$lt": [
                                        "$created_at",
                                        {
                                            "$dateAdd": {
                                                "startDate": "$$today",
                                                "unit": "day",
                                                "amount": 1
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            ],
            "as": "notification_info"
        }
    },
    {
        "$match": {
            "notification_info": { "$eq": [] }  # Only include tasks with no email sent today
        }
    },
    {
        "$lookup": {
            "from": "employee",
            "let": {
                "assigned_to": "$assignedTo",
                "country_filter": "India"
            },
            "pipeline": [
                {
                    "$match": {
                        "$expr": {
                            "$and": [
                                { "$eq": [{ "$toString": "$_id" }, "$$assigned_to"] },
                                {
                                    "$or": [
                                        { "$eq": ["$country", "$$country_filter"] },
                                        { "$eq": ["$address.country", "$$country_filter"] }
                                    ]
                                },
                                { "$ne": ["$deleted", True] }
                            ]
                        }
                    }
                }
            ],
            "as": "assigned_employee"
        }
    },
    {
        "$lookup": {
            "from": "orderBook",
            "localField": "secondaryId",
            "foreignField": "purchaseOrderNumber",
            "as": "order_data"
        }
    },
    {
        "$lookup": {
            "from": "supplierOrderBook",
            "let": {
                "orderBookId": { "$arrayElemAt": ["$order_data.orderBookId", 0] }
            },
            "pipeline": [
                {
                    "$match": {
                        "$expr": {
                            "$eq": ["$linkedCOBId", "$$orderBookId"]
                        }
                    }
                }
            ],
            "as": "supplier_order_data"
        }
    },
    {
        "$project": {
            "name": 1,
            "entityId": 1,
            "orderId": 1,
            "secondaryId": 1,
            "status": 1,
            "dueDate": 1,
            "lastUpdatedAt": 1,
            "assignedTo": 1,
            "customerName": 1,
            "orderBookId": { "$arrayElemAt": ["$order_data._id", 0] },
            "employee_email": { "$arrayElemAt": ["$assigned_employee.email", 0] },
            "employee_name": { "$arrayElemAt": ["$assigned_employee.name", 0] },
            "timezone": { "$arrayElemAt": ["$assigned_employee.timezone", 0] },
            "mrd": { "$arrayElemAt": ["$supplier_order_data.mrd", 0] }
        }
    }
]

        logging.info("MongoDB aggregation pipeline constructed.")
        return list(collection.aggregate(pipeline))

    def close_connections(self):
        """Close all database connections."""
        if self.sales_db:
            self.sales_db.close()
            logging.info("Sales DB connection closed.")
        if self.procure_db:
            self.procure_db.close()
            logging.info("Procure DB connection closed.")
        if self.logi_db:
            self.logi_db.close()
            logging.info("Logi DB connection closed.")

    def get_action_required_tasks(self):
        """Get tasks that require action from all stacks."""
        logging.info("Fetching action required tasks from all stacks.")
        return {
            'SalesStack': self.get_sales_stack_sla_breach_tasks(),
            'ProcureStack': self.get_procure_stack_sla_breach_tasks(),
            'LogiStack': self.get_logi_stack_sla_breach_tasks()
        }

    def get_action_link(self, stack, enquiry_id, owner=''):
        """Generates the action link for a given task based on stack and owner."""
        base_url = None
        if stack == 'ProcureStack':
            base_url = Variable.get("procure_admin_url", default_var=None) if owner == 'category_head' else Variable.get("procure_dashboard_url", default_var=None)
        elif stack == 'SalesStack':
            base_url = Variable.get("salesstack_url", default_var=None)
        elif stack == 'LogiStack':
            base_url = Variable.get("logistack_url", default_var=None)

        if base_url:
            return base_url.format(enquiry_id=enquiry_id)
        logging.warning(f"No action link found for stack: {stack} and owner: {owner}")
        return None

    def insert_email_notification_history(self, tasks, stack):
        """Inserts email notification history into the respective database."""
        for data in tasks:
            insert_dict = {
                "email_type": "sla_breach",
                "document_ref_id": data.get('id'),
                "state": str(data.get('state')).replace(' ','_').lower()
            }
            try:
                if stack == 'SalesStack':
                    self.sales_db.write_data(self.insert_query, insert_dict)
                elif stack == 'ProcureStack': # Assuming 'Procure' is the stack name for procure_db
                    self.procure_db.write_data(self.insert_query, insert_dict)
                else:
                    logging.warning(f"Unsupported stack for insertion: {stack}")
                    continue
                logging.info(f"Inserted SLA breach notification for document_ref_id: {data.get('id')} in {stack} stack.")
            except Exception as e:
                logging.error(f"Error inserting data into {stack} DB for {data.get('id')}: {e}")

    def insert_logi_email_notification_history(self, tasks):
        """Inserts email notification history for Logi stack into MongoDB."""
        for final_dict in tasks:
            notification_doc = {
                "email_type": "sla_breach",
                "document_ref_id": str(final_dict.get('activity_id')),
                "state": final_dict.get('state'),
                "created_at": datetime.now(ZoneInfo("Asia/Kolkata"))
            }
            try:
                self.logi_db.write_data("email_notification_history", notification_doc)
                logging.info(f"Stored Logi SLA notification for activity_id: {final_dict.get('activity_id')}")
            except Exception as e:
                logging.error(f"Error storing Logi notification for {final_dict.get('activity_id')}: {e}")

    def _is_working_hour(self, dt):
        """Checks if a given datetime falls within working hours (9 AM - 8 PM) and working days."""
        if not (9 <= dt.hour < 20):
            return False
        if dt.weekday() == 5 and dt.hour >= 14:  # Saturday after 2 PM
            return False
        if dt.weekday() == 6:  # Sunday
            return False
        if dt.weekday() == 0 and dt.hour < 9:  # Monday before 9 AM
            return False
        return True

    def _get_formatted_timestamp(self, dt, tz_str):
        """Formats a datetime object to a string with timezone abbreviation."""
        try:
            _tz = pytz.timezone(tz_str)
            dt_ = dt.astimezone(_tz)
            return dt_
        except Exception as e:
            logging.error(f"Error formatting timestamp {dt} with timezone {tz_str}: {e}")
            return None

    def add_tat_excluding_non_working_hours(self, email, last_updated_utc, tat_hours, timezone = 'Asia/Kolkata'):
        """
        Calculates the due date by adding TAT, excluding non-working hours and weekends.
        Returns the calculated datetime, formatted timestamp, and timezone.
        """
        timezone  = self.loc_map.get(email, timezone)
        if not last_updated_utc:
            logging.warning("last_updated_utc is None, cannot calculate TAT.")
            return None, None, timezone

        ts = pd.Timestamp(last_updated_utc)
        tz_zone = timezone

        # Localize to UTC if timezone is not present, then convert to target timezone
        current_dt_local = ts.tz_convert(tz_zone) if ts.tz is not None else ts.tz_localize('UTC').tz_convert(tz_zone)

        if not tat_hours:
            tat_hours = 12 # Default TAT

        hours_added = 0
        # Round up to the next hour if minutes/seconds exist
        if current_dt_local.minute > 0 or current_dt_local.second > 0:
            current_dt_local = (current_dt_local + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)

        while hours_added < tat_hours:
            if self._is_working_hour(current_dt_local):
                hours_added += 1
            current_dt_local += timedelta(hours=1)

        if pd.isna(current_dt_local):
            logging.error("Calculated current_dt_local is NaT (Not a Time).")
            return None, None, tz_zone

        formatted_timestamp = self._get_formatted_timestamp(current_dt_local, tz_zone)
        return current_dt_local, formatted_timestamp, tz_zone

    def _prepare_common_task_data(self, task, stack, email, dt_date, formatted_tp, tz_info):
        """Prepares common task data dictionary for all stacks."""
        tz_info_obj = pytz.timezone(tz_info)
        overdue = datetime.now(tz=tz_info_obj) - dt_date
        overdue_hours = overdue.total_seconds() / 3600

        if overdue_hours > 0:
            action_link_owner = ''
            if stack == 'ProcureStack':
                # Determine owner for action link for ProcureStack
                transition_key = f"{task.get('state', '')}_to_{task.get('to_state', '')}"
                action_link_owner = self.workflow_mapping.get(stack, {}).get('transitions', {}).get(transition_key, {}).get('owner', '')
            elif stack == 'SalesStack':
                action_link_owner = 'SalesStack' # Specific to sales stack logic

            action_link = self.get_action_link(stack, task.get('enquiry_id'), action_link_owner)
            action_link_name = self.workflow_mapping.get(stack, {}).get('states', {}).get(task.get('state'), {}).get('action_required', 'No action required')

            return {
                **task,
                'action_link': action_link,
                'action_link_name': action_link_name,
                'email': email,
                'state' : str(task.get('state')).replace('_',' ').title(),
                'stack_name': stack,
                'generation_time': datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST'),
                'due_date': formatted_tp,
                'over_due': round(overdue_hours, 2)
            }
        return None

    def prepare_procure_and_sales_task_data(self, tasks, stack):
        """Prepare task data with all required information for Procure and Sales stacks."""
        filtered_tasks = []
        for task in tasks:
            email = None
            # For SalesStack, use the real owner email only
            if stack == 'SalesStack':
                email = task.get('owner_name')
                logging.info(f"Owner email for SalesStack task: {email}")
              
            if not email:
                logging.warning(f"Skipping task due to missing owner email: {task.get('enquiry_id')}")
                continue
            #logging.info(f"Task: {task.get('enquiry_id')}, State: {task.get('state')}, Expiry Date: {task.get('expiry_date')}")
            if (stack=='SalesStack' 
                and str(task.get('state')) == 'pricing_quotation_generated' and task.get('expiry_date') is None
            
         ):
                logging.info(f"Skipping expired SalesStack task: enquiry_id={task.get('enquiry_id')}, expiry_date={task.get('expiry_date')}")
 
                continue
            if ((stack=='SalesStack' and str(task.get('state')) == 'pricing_quotation_generated')
                and task.get('expiry_date')
                            and task['expiry_date'].date() < datetime.now(timezone.utc).date()):
                logging.info(f"Skipping expired SalesStack task: enquiry_id={task.get('enquiry_id')}, expiry_date={task.get('expiry_date')}")
                continue

            task_timezone = task.get('timezone') or DEFAULT_TIMEZONES.get(self.country)
            dt_date, formatted_tp, tz_info = self.add_tat_excluding_non_working_hours(
                email, task.get('due_date'), task.get('tat_hours'), task_timezone
            )
            if dt_date is None:
                logging.warning(f"Skipping task due to invalid TAT calculation for {task.get('enquiry_id')}")
                continue

            prepared_task = self._prepare_common_task_data(task, stack, email, dt_date, formatted_tp, tz_info)
            if prepared_task:
                filtered_tasks.append(prepared_task)
        return filtered_tasks

    def prepare_logi_task_data(self, tasks, stack):
        """Prepare task data with all required information for Logi stack."""
        prepared_logi_tasks = []
        for task in tasks:
            action_link = self.get_action_link(stack, task.get('order_id'))
            owner_name = ' '.join(word.capitalize() for word in str(task.get('owner_name') or '@').replace('.', ' ').split('@')[0].split(' '))
            prepared_logi_tasks.append({
                **task,
                'action_link': action_link,
                'owner_name': owner_name,
                'stack_name': stack,
                'generation_time': datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST'),
            })
        return prepared_logi_tasks

    def _group_tasks_by_owner(self, tasks):
        """Group tasks by owner email."""
        tasks_by_owner = defaultdict(list)
        for task in tasks:
            owner_email = task.get('email') or task.get('owner_name')  # Use 'email' if present, otherwise 'owner_name'
            if owner_email:
                if isinstance(owner_email, list):
                    for email in owner_email:
                        if email:
                            tasks_by_owner[email].append(task)
                        else:
                            logging.warning(f"Task {task.get('enquiry_id')} has an empty email in the list for grouping.")
                else:
                    tasks_by_owner[owner_email].append(task)
            else:
                logging.warning(f"Task {task.get('enquiry_id')} has no owner email/name for grouping.")
        return tasks_by_owner


    def render_email_template(self, template_name, **kwargs):
        """Render the email template using Jinja2."""
        try:
            template = self.template_env.get_template(template_name)
            return template.render(**kwargs)
        except Exception as e:
            logging.error(f"Error rendering email template {template_name}: {e}")
            raise

    def send_email(self, recipients, html_content, stack = 'SalesStack'):
        """Send email using SES."""
        recipients += ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        

        if not recipients:
            logging.warning("No recipients provided for email, skipping send.")
            return

        # Ensure recipients is a list
        if isinstance(recipients, str):
            recipients = [r.strip() for r in recipients.split(',') if r.strip()]
        
        logging.info(f"Attempting to send email to: {', '.join(recipients)}")
        try:
            response = self.ses_hook.send_email(
                sender=self.sender_email,
                recipient= recipients,
                subject= f"{stack} - SLA Violation",
                html_body=html_content
            )
            message_id = response.get('MessageId', 'unknown').replace('\n', '').replace('\r', '')
            logging.info(f"Email sent successfully to {', '.join(recipients)}! Message ID: {message_id}")
        except Exception as e:
            logging.error(f"Error sending email to {', '.join(recipients)}: {e}")

    def _add_head_emails(self, tasks):
        """Categorizes tasks by overdue hours and identifies a potential head email."""
        first_level_escalation_tasks = [] # > 24 hours overdue
        second_level_escalation_tasks = [] # > 48 hours overdue
        owner_tasks = [] # <= 24 hours overdue
        head_email = ''

        for task in tasks:
            over_due_hours = task.get('over_due', 0)
            if over_due_hours > 48:
                second_level_escalation_tasks.append(task)
                if not head_email and task.get('bu_head_email'): # Assign head email from the first relevant task
                    head_email = task.get('bu_head_email')
            elif over_due_hours > 24:
                first_level_escalation_tasks.append(task)
                if not head_email and task.get('bu_head_email'):
                    head_email = task.get('bu_head_email')
            else:
                owner_tasks.append(task)
        
        return first_level_escalation_tasks, second_level_escalation_tasks, owner_tasks, head_email

    def _process_and_send_emails(self, tasks_by_owner, stack_name, template_name='sla_violation_email_template.html'):
        """Helper to process grouped tasks and send emails."""
        for owner_email, owner_tasks in tasks_by_owner.items():
            if not owner_tasks:
                continue

            first_tasks, second_tasks, current_owner_tasks, head_email = self._add_head_emails(owner_tasks)
            
            # Recipient list initialization for primary owner
            primary_recipients = [owner_email] if owner_email and owner_email.lower() != 'nan' else []

            # Send email to the primary owner for immediate breaches
            if current_owner_tasks:
                owner_name = ' '.join(word.capitalize() for word in (owner_email or 'Team').replace('.', ' ').split('@')[0].split(' '))
                html_content = self.render_email_template(
                    template_name,
                    tasks=current_owner_tasks,
                    stack_name=stack_name,
                    owner_name=owner_name,
                    generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
                )
                self.send_email(primary_recipients, html_content,stack_name)
                self.insert_email_notification_history(owner_tasks, stack_name)

            # Send email to first-level escalation (owner + category head if available)
            if first_tasks:
                owner_name = ' '.join(word.capitalize() for word in (owner_email or 'Team').replace('.', ' ').split('@')[0].split(' '))
                recipients = list(set(primary_recipients + ([head_email] if head_email else [])))
                html_content = self.render_email_template(
                    template_name,
                    tasks=first_tasks,
                    stack_name=stack_name,
                    owner_name=owner_name,
                    generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
                )
                self.send_email(recipients, html_content, stack_name)

            # Send email to second-level escalation (owner + category head + higher management if available)
            if second_tasks:
                owner_name = ' '.join(word.capitalize() for word in (owner_email or 'Team').replace('.', ' ').split('@')[0].split(' '))
                # Example for higher management, adjust as needed
                higher_management_email = '' # This needs to be dynamically fetched or configured
                recipients = list(set(primary_recipients + ([head_email] if head_email else []) + [higher_management_email]))
                html_content = self.render_email_template(
                    template_name,
                    tasks=second_tasks,
                    stack_name=stack_name,
                    owner_name=owner_name,
                    generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
                )
                self.send_email(recipients, html_content, stack_name)

    def process_procure_action_emails(self):
        """Processes action required emails for Procure stack."""
        logging.info("Starting processing for Procure stack action emails.")
        tasks = self.get_procure_stack_sla_breach_tasks()
        if not tasks:
            logging.info("No tasks to process for Procure stack.")
            return

        prepared_tasks = self.prepare_procure_and_sales_task_data(tasks, 'ProcureStack')
        tasks_by_owner = self._group_tasks_by_owner(prepared_tasks)
        self._process_and_send_emails(tasks_by_owner, 'ProcureStack')
        logging.info("Finished processing for Procure stack action emails.")

    def _build_owner_to_bu_head_lookup(self):
        """Builds a lookup dict: owner_email -> set of bu_head_emails from the owner_to_bu_head list."""
        lookup = defaultdict(set)
        for entry in owner_to_bu_head:
            owner = entry.get('email', '').lower()
            bu_head = entry.get('bu_head', '').lower()
            if owner and bu_head:
                lookup[owner].add(bu_head)
        return lookup

    def process_sales_action_emails(self):
        """Processes action required emails for Sales stack, sending only to BU heads for each owner, grouped by BU head."""
        logging.info("Starting processing for Sales stack action emails.")
        tasks = self.get_sales_stack_sla_breach_tasks()
        if not tasks:
            logging.info("No tasks to process for Sales stack.")
            return

        prepared_tasks = self.prepare_procure_and_sales_task_data(tasks, 'SalesStack')
        owner_to_bu_head_lookup = self._build_owner_to_bu_head_lookup()
        # Group tasks by bu_head_email
        ss = {}
        for kk in prepared_tasks:
            sdr = kk

            ss[hash(str(kk.get('enquiry_id')+kk.get('action_required') + kk.get('customer_name') ))] = kk
        prepared_tasks = list(ss.values())
        total_count = 0
        bu_head_to_tasks = defaultdict(list)
        for task in prepared_tasks:
            owner_email = (task.get('email') or task.get('owner_name') or '').lower()
            bu_heads = owner_to_bu_head_lookup.get(owner_email, set())
            if not bu_heads:
                logging.warning(f"No BU head found for owner_email: {owner_email}")
            for bu_head in bu_heads:
                bu_head_to_tasks[bu_head].append(task)
        
        for bu_head_email, tasks_for_head in bu_head_to_tasks.items():
            tz = ZoneInfo(self.loc_map.get(bu_head_email, 'Asia/Kolkata'))
            for task in tasks_for_head:
                due_date_str = task.get('due_date')
                if due_date_str:
                    due_date = due_date_str
                    due_date = due_date.replace(tzinfo=tz)
                    task['due_date'] = str(due_date.strftime("%b %d %Y %H:%M %Z")).replace('+04', 'GST')

        # Log the final mapping
        for bu_head_email, tasks_for_head in bu_head_to_tasks.items():
            logging.info(f"BU head {bu_head_email} will receive {len(tasks_for_head)} tasks.")
            owner_category_pairs = set()
            for task in list(tasks_for_head):
                owner_email = (task.get('email') or task.get('owner_name') or '').lower()
                category = task.get('category', 'Unknown')
                owner_category_pairs.add((owner_email, category))
            logging.info(f"BU head {bu_head_email} is mapped to owners and categories: {owner_category_pairs}")
        # Send one email per BU head
        for bu_head_email, tasks_for_head in bu_head_to_tasks.items():
            if not bu_head_email or not tasks_for_head:
                continue

            # The 'owner_name' is now the BU Head's name, personalized.
            bu_head_name = bu_head_email.split('@')[0].split('.')[0].capitalize()
            fn_map = defaultdict(list)
            for tsk in tasks_for_head:
                fn_map[tsk.get('action_link_name')].append(tsk)
            

            # Now sort each list in fn_map by due_date
            for key in fn_map:
                fn_map[key].sort(key=lambda x: x.get('due_date'))
                total_count += len(fn_map[key])

            
            # Ensure all tasks are sent in one email, not split by overdue status
            html_content = self.render_email_template(
                'sla_violation_email_template.html',
                tasks=fn_map,
                stack_name='SalesStack',
                owner_name=bu_head_name,
                generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
            )
            self.send_email([bu_head_email], html_content, 'SalesStack')
            self.insert_email_notification_history(tasks_for_head, 'SalesStack')
        logging.info(f"Total tasks sent: {total_count}")
        logging.info("Finished processing for Sales stack action emails.")

    def process_logi_stack_action_emails(self):
        """Processes action required emails for Logi stack."""
        logging.info("Starting processing for Logi stack action emails.")
        tasks = self.get_logi_stack_sla_breach_tasks()
        if not tasks:
            logging.info("No tasks to process for Logi stack.")
            return

        prepared_tasks = self.prepare_logi_task_data(tasks, 'LogiStack')
        tasks_by_owner = self._group_tasks_by_owner(prepared_tasks)

        default_logi_recipients = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]

        for owner_email, owner_tasks in tasks_by_owner.items():
            if not owner_tasks:
                continue

            recipients = []
            if owner_email and owner_email.lower() != 'nan':
                recipients.append(owner_email)
                owner_name = ' '.join(word.capitalize() for word in owner_email.replace('.', ' ').split('@')[0].split(' '))
            else:
                recipients = default_logi_recipients
                owner_name = "Team" # Default owner name for undefined email

            html_content = self.render_email_template(
                'sla_violation_email_template.html',
                tasks=owner_tasks,
                stack_name='LogiStack',
                owner_name=owner_name,
                generation_time=datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST')
            )
            # Sending to a hardcoded email for testing/review, adjust as needed
            self.send_email(['<EMAIL>'], html_content, 'LogiStack')
            self.insert_logi_email_notification_history(owner_tasks)
        logging.info("Finished processing for Logi stack action emails.")

    def get_procure_stack_sla_breach_tasks(self):
        """Get tasks from ProcureStack that require action."""
        # This query needs to be filled with actual logic to fetch SLA breach tasks
        sla_breach_proc_query = f"""
                        WITH tat_mapping AS (
                    SELECT * FROM (VALUES {procure_stack_sla}
                    ) AS t(from_state, to_state, action_required, tat_hours)
                ),
                state_transitions AS (
                    SELECT 
                        s1.enquiry_id,
                        s1.current_status AS from_state,
                        s1.created_at AS from_time,
                        tm.to_state,
                        tm.action_required,
                        tm.tat_hours,
                        (s1.created_at + (tm.tat_hours || ' hours')::interval)::date AS cl_due_date,
                        (s1.created_at + (tm.tat_hours || ' hours')::interval) as due_date
                    FROM tat_mapping tm 
                    inner join 
                    (
                    select current_status, id as enquiry_id, last_status_change as created_at from enquiries 
                    union all 
                    select status as current_status, enquiry_id, last_status_change as created_at from sample_requests
                    ) s1
                    ON s1.current_status = tm.from_state
                    
                    -- FROM status_changes s1
                    -- JOIN tat_mapping tm ON s1.new_state = tm.from_state
                    -- WHERE NOT EXISTS (
                    --     SELECT 1 FROM status_changes s2
                    --     WHERE s2.enquiry_id = s1.enquiry_id
                    --     AND s2.new_state = tm.to_state
                    -- )
                ),
                bu_head_emails_agg AS (
                    SELECT
                        bu.id AS bu_id,
                        string_agg(DISTINCT bh.email, ', ') AS bu_head_emails
                    FROM business_units bu
                    JOIN profiles bh ON bu.id = bh.business_unit_id AND bh.role_code = 'bu_head'
                    GROUP BY bu.id
                )

                SELECT
                    e.id,
                    e.enquiry_id,
                    s.from_state AS state,
                    s.to_state,
                    s.from_time,
                    s.due_date,
                    e.customer_full_name AS customer_name, 
                    p.full_name AS procurement_poc,
                    p.email AS owner_email,
                    bhagg.bu_head_emails AS bu_head_emails,
                    s.action_required,
                    p.country,
                    p.timezone
                FROM state_transitions s
                LEFT JOIN enquiries e ON s.enquiry_id = e.id
                LEFT JOIN profiles p ON CAST(p.id AS text) = e.procurement_poc_id
                LEFT JOIN business_units bu ON e.category = bu.name
                LEFT JOIN bu_head_emails_agg bhagg ON bu.id = bhagg.bu_id
                LEFT JOIN email_notification_history en 
                    ON e.id::text = en.document_ref_id 
                    AND s.from_state = en.state 
                    AND email_type = 'sla_breach' 
                    AND en.created_at::date = NOW()::date
                WHERE 
                    en.id IS NULL 
                    AND p.country = :country  
                    AND e.created_at::date > '2025-05-01';

        """
        logging.info("Running Procure stack SLA breach query.")
        return self.procure_db.read_data(sla_breach_proc_query, {'country': self.country})

    def get_sales_stack_sla_breach_tasks(self):
        """Get tasks from SalesStack that require action."""
        # This query needs to be filled with actual logic to fetch SLA breach tasks
        sales_stack_sla_query = f"""
            WITH tat_mapping AS (
            SELECT * FROM (VALUES 
            {sales_stack_sla}
            ) AS t(from_state, to_state, action_required, tat_hours)
            ),
            -- Identify status changes per enquiry (looking at the from_state only)
            ordered_statuses AS (
            SELECT
                es.enquiry_id,
                cast(es.status as text) AS from_state,
                es.created_at AS from_time,
                qo.expiry_date
            FROM enquiry_status_history es
            left join quote_generation_details qd on es.id = qd.status_history_id
            left join quote_generation_options qo on qd.id = qo.quote_generation_id
            union all
            select enquiry_id, sample_status, changed_at, Null as expiry_date from sample_status_history
            ),
            msk as (
                select au.email as head_email, e.sales_team_member, ur.category , ur.country, ur.timezone from enquiries e
                left join user_roles ur on e.category = ur.category
                left join auth.users au on ur.user_id::text = au.id::text
            )
            ,
            -- Match each state with its TAT and calculate the due date
            ask as (
            SELECT
            distinct case when sr.id is null then cast(e.id as text) else sr.id end as id,
            e.enquiry_id,
            e.category,
            s.from_state as state,
            s.from_time,
            tm.tat_hours,
            tm.action_required,
            e.customer_full_name as customer_name,
            e.sales_team_member as owner_name,
            (s.from_time + (tm.tat_hours || ' hours')::interval)::date AS calculated_due_date,
            (s.from_time + (tm.tat_hours || ' hours')::interval) as due_date,
            s.expiry_date
            FROM ordered_statuses s
            left join enquiries e on e.id = s.enquiry_id
            left join sample_requests sr on e.id = sr.enquiry_id
            JOIN tat_mapping tm
            ON s.from_state::text = tm.from_state
            WHERE NOT EXISTS (
                SELECT 1 FROM ordered_statuses s2
                WHERE s2.enquiry_id = s.enquiry_id
                AND s2.from_state = tm.to_state
        ) and e.current_status::text = s.from_state or sr.status = s.from_state and e.created_at::date > '2025-04-01' 
        AND COALESCE(e.current_status::text, sr.status) NOT IN ('cancelled', 'canceled')

            ) SELECT distinct a.* , msk.category from ask a
            left join msk on a.owner_name = msk.sales_team_member
            LEFT JOIN email_notification_history en on a.id::text = en.document_ref_id and a.state = en.state and email_type = 'sla_breach' and en.created_at::date = Now()::date
            where en.id is null ;
            -- and msk.country = :country ;
        """
        logging.info("Running Sales stack SLA breach query.")
        return self.sales_db.read_data(sales_stack_sla_query, {'country': self.country})

    def parse_sla_to_hours(self, sla_text):
        """Parses SLA text (e.g., '24 hours', '3 days') to total hours."""
        # if not sla_text or "MRD" in sla_text:
        #     logging.debug(f"Skipping SLA parsing for MRD or empty text: {sla_text}")
        #     return None

        sla_text_lower = sla_text.strip().lower()
        if "hours" in sla_text_lower or "hrs" in sla_text_lower:
            match = re.search(r"(\d+)", sla_text_lower)
            if match:
                return int(match.group(1))
        elif "days" in sla_text_lower:
            match = re.search(r"(\d+)(-(\d+))?", sla_text_lower)
            if match:
                # If a range like '2-3 days', take the upper bound (3 days)
                upper_bound_days = match.group(3) if match.group(3) else match.group(1)
                return int(upper_bound_days) * 24
        logging.warning(f"Could not parse SLA text to hours: {sla_text}")
        return None

    def get_logi_stack_sla_breach_tasks(self):
        """Get tasks from LogiStack that require action (SLA breach)."""
        logging.info("Fetching Logi stack activities for SLA breach assessment.")
        docs = self.get_logi_pipeline()
        po_grouped_docs = defaultdict(list)

        for doc in docs:
            po_id = doc.get('orderId', 'unknown') # Use 'orderId' as per projection
            po_grouped_docs[po_id].append(doc)

        sla_breach_tasks = []
        

        for po_id, doc_list in po_grouped_docs.items():
            states_present = {doc.get('name', '').lower(): doc for doc in doc_list}
            for from_state, to_state, sla_text in steps:
                from_state_lower = from_state.lower()
                to_state_lower = to_state.lower()

                if from_state_lower in states_present and to_state_lower in states_present:
                    from_doc = states_present[from_state_lower]
                    to_doc = states_present[to_state_lower]

                    # Check for status completion for 'from' state and 'TODO' for 'to' state
                    if not (from_doc.get('status') == 'COMPLETED' and to_doc.get('status') == 'TODO'):
                        logging.debug(f"Skipping Logi task for PO {po_id} due to status mismatch: {from_state} ({from_doc.get('status')}) -> {to_state} ({to_doc.get('status')})")
                        continue

                    owner_email = from_doc.get('employee_email', '')
                    owner_name = ' '.join(word.capitalize() for word in (owner_email or 'Team').replace('.', ' ').split('@')[0].split(' '))

                    due_date_raw = from_doc.get("lastUpdatedAt")
                    if not due_date_raw:
                        logging.warning(f"Missing lastUpdatedAt for Logi task {from_doc.get('orderId')} - {from_state}. Skipping SLA check.")
                        continue

                    
                    tat_hours = self.parse_sla_to_hours(sla_text)
                    if 'mrd' in sla_text.lower():
                        if '-' in sla_text.lower():
                            tat_hours = -tat_hours
                        elif '+' in sla_text.lower():
                            tat_hours = tat_hours
                        else:
                            tat_hours = 0
                        due_date_raw = from_doc.get("mrd")
                    
                    if tat_hours is None:
                        logging.warning(f"Could not determine TAT hours for Logi task {from_doc.get('orderId')} - {from_state} to {to_state}. Skipping SLA check.")
                        continue

                    calculated_due_dt, formatted_timestamp, tz_info = self.add_tat_excluding_non_working_hours(owner_email, due_date_raw, tat_hours, from_doc.get('timezone', DEFAULT_TIMEZONES.get(self.country, 'Asia/Kolkata')))
                    now_utc = datetime.now(ZoneInfo(tz_info))
                    if calculated_due_dt is None:
                        logging.warning(f"Failed to calculate due date for Logi task {from_doc.get('orderId')} - {from_state}. Skipping SLA check.")
                        continue

                    is_overdue = now_utc > calculated_due_dt.astimezone(ZoneInfo(tz_info)) # Compare in UTC
                    overdue_hours = round((now_utc - calculated_due_dt.astimezone(ZoneInfo(tz_info))).total_seconds() / 3600, 2) if is_overdue else None

                    key = from_state + '  ' + to_state
                    action_link_name = sample_flow_mapping.get(key, 'No action required')

                    if is_overdue:
                        task_dict = {
                            "activity_id": str(from_doc.get('_id')), # Use MongoDB _id for document_ref_id
                            "state": from_state.replace('_', ' ').title() if '_' in from_state else from_state,
                            "customer_name": from_doc.get('customerName', ''),
                            'order_id' : from_doc.get('orderBookId', ''),
                            "enquiry_id": from_doc.get('orderId', ''),
                            "owner_name": owner_name,
                            "due_date": formatted_timestamp,
                            "generation_time": datetime.now(ZoneInfo("Asia/Kolkata")).strftime('%Y-%m-%d %H:%M:%S IST'),
                            "sla_duration": sla_text,
                            "from_state": from_state,
                            "to_state": to_state,
                            "is_overdue": is_overdue,
                            "over_due": overdue_hours,
                            'email': owner_email,
                            'action_link_name': action_link_name
                        }
                        sla_breach_tasks.append(task_dict)
                        logging.info(f"Identified Logi SLA breach for PO {po_id}, from {from_state} to {to_state}. Overdue by {overdue_hours} hours.")
        logging.info(f"Found {len(sla_breach_tasks)} Logi stack SLA breach tasks.")
        return sla_breach_tasks


# Mapping country to timezone & schedule
COUNTRY_TIMEZONE_SCHEDULE = {
    "India": {"tz": "Asia/Kolkata", "cron": "30 3 * * 1-6"},       # 9:00 AM IST = 3:30 AM UTC
    "USA": {"tz": "America/New_York", "cron": "0 13 * * 1-6"},     # 9:00 AM EDT = 1:00 PM UTC
    "Canada": {"tz": "America/Toronto", "cron": "0 13 * * 1-6"},   # 9:00 AM EDT = 1:00 PM UTC
    "UAE": {"tz": "Asia/Dubai", "cron": "0 5 * * 1-6"},            # 9:00 AM GST = 5:00 AM UTC
    "China": {"tz": "Asia/Shanghai", "cron": "0 1 * * 1-6"},       # 9:00 AM CST = 1:00 AM UTC
}

def run_all_email_tasks_for_country(country): # Renamed for clarity
    """Initializes and runs all email processing tasks for a given country."""
    logging.info(f"Starting email processing for country: {country}")
    manager = EmailNotificationManager(country)
    try:
        manager.initialize_connections()
        manager.process_sales_action_emails()
        manager.process_procure_action_emails()
        manager.process_logi_stack_action_emails()
    except Exception as e:
        logging.error(f"An error occurred during email task execution for {country}: {e}")
    finally:
        manager.close_connections()
    logging.info(f"Completed email processing for country: {country}")

def create_email_task_operator(dag, country, method_name):
    """Creates a PythonOperator for a specific email processing method."""
    def task_callable(**kwargs):
        logging.info(f"Executing task '{method_name}' for {country}.")
        manager = EmailNotificationManager(country)
        try:
            manager.initialize_connections()
            getattr(manager, method_name)()
        except Exception as e:
            logging.error(f"Error in task '{method_name}' for {country}: {e}")
            raise # Re-raise the exception to mark the task as failed
        finally:
            manager.close_connections()
        logging.info(f"Finished executing task '{method_name}' for {country}.")

    return PythonOperator(
        task_id=f"{method_name.replace('process_', '').replace('_action_emails', '')}_{country.lower()}",
        python_callable=task_callable,
        dag=dag,
    )

# Dynamically create DAGs for each country
for country, info in COUNTRY_TIMEZONE_SCHEDULE.items():
    local_tz = pendulum.timezone(info["tz"])
    dag_id = f"sla_email_dag_{country.lower()}"

    with DAG(
        dag_id=dag_id,
        default_args=DEFAULT_DAG_ARGS,
        description=f'Send email notifications for SLA breaches in {country}',
        schedule=info["cron"],
        catchup=False,
        start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
        tags=['email', 'notifications', country, 'sla'],
        dagrun_timeout=timedelta(minutes=15),
    ) as dag:
        sales_task = create_email_task_operator(dag, country, "process_sales_action_emails")
        # procure_task = create_email_task_operator(dag, country, "process_procure_action_emails")
        # logi_task = create_email_task_operator(dag, country, "process_logi_stack_action_emails")

        # Define task dependencies (run in parallel)
        # [sales_task, procure_task, logi_task]
        [sales_task]

    # Register the DAG in the global scope
    globals()[dag_id] = dag