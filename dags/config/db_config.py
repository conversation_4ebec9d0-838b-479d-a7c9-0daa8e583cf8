from airflow.models import Variable
from typing import Dict, Any, <PERSON><PERSON>
import os
from .default import DEFAULT_SYNC_CONFIG
import logging

class DatabaseConfig:
    @staticmethod
    def get_sales_stack_postgres_params() -> Dict[str, Any]:
        """
        Get PostgresSQL connection parameters from Airflow variables with defaults from environment
        :return: Dictionary containing PostgresSQL connection parameters
        """
        return {
            'dbname': Variable.get('sales_stack_db_name', default_var=os.getenv('SALES_STACK_DB_NAME')),
            'user': Variable.get('sales_stack_db_user', default_var=os.getenv('SALES_STACK_DB_USER')),
            'password': Variable.get('sales_stack_db_password', default_var=os.getenv('SALES_STACK_DB_PASSWORD')),
            'host': Variable.get('sales_stack_db_host', default_var=os.getenv('SALES_STACK_DB_HOST')),
            'port': Variable.get('sales_stack_db_port', default_var=os.getenv('SALES_STACK_DB_PORT'))
        }

    @staticmethod
    def get_procuro_stack_postgres_params() -> Dict[str, Any]:
        """
        Get PostgresSQL connection parameters from Airflow variables with defaults from environment
        :return: Dictionary containing PostgresSQL connection parameters
        """
        return {
            'dbname': Variable.get('procure_stack_db_name', default_var=os.getenv('PROCURE_STACK_DB_NAME')),
            'user': Variable.get('procure_stack_db_user', default_var=os.getenv('PROCURE_STACK_DB_USER')),
            'password': Variable.get('procure_stack_db_password', default_var=os.getenv('PROCURE_STACK_DB_PASSWORD')),
            'host': Variable.get('procure_stack_db_host', default_var=os.getenv('PROCURE_STACK_DB_HOST')),
            'port': Variable.get('procure_stack_db_port', default_var=os.getenv('PROCURE_STACK_DB_PORT'))
        }

    @staticmethod
    def get_reporting_stack_postgres_params() -> Dict[str, Any]:
        """
        Get PostgresSQL connection parameters from Airflow variables with defaults from environment
        :return: Dictionary containing PostgresSQL connection parameters
        """
        return {
            'dbname': Variable.get('reporting_stack_db_name', default_var=os.getenv('REPORTING_STACK_DB_NAME')),
            'user': Variable.get('reporting_stack_db_user', default_var=os.getenv('REPORTING_STACK_DB_USER')),
            'password': Variable.get('reporting_stack_db_password', default_var=os.getenv('REPORTING_STACK_DB_PASSWORD')),
            'host': Variable.get('reporting_stack_db_host', default_var=os.getenv('REPORTING_STACK_DB_HOST')),
            'port': Variable.get('reporting_stack_db_port', default_var=os.getenv('REPORTING_STACK_DB_PORT'))
        }

    @staticmethod
    def get_logi_stack_mongo_params() -> Tuple[str, str]:
        """
        Get MongoDB connection parameters from Airflow variables with defaults from environment
        :return: Tuple containing (connection_string, database_name)
        """

        return (
            Variable.get('logi_stack_mongo_connection_string', default_var=os.getenv('LOGI_STACK_MONGO_CONNECTION_STRING')),
            Variable.get('logi_stack_mongo_db_name', default_var=os.getenv('LOGI_STACK_MONGO_DB_NAME'))
        )

    @staticmethod
    def get_sync_config() -> Dict[str, Any]:
        """
        Get synchronization configuration from Airflow variables with defaults from default.py
        :return: Dictionary containing sync configuration
        """
        return {
            'postgres_table': Variable.get('sync_postgres_table', default_var=DEFAULT_SYNC_CONFIG['postgres_table']),
            'mongo_collection': Variable.get('sync_mongo_collection', default_var=DEFAULT_SYNC_CONFIG['mongo_collection']),
            'sync_interval_days': int(Variable.get('sync_interval_days', default_var=DEFAULT_SYNC_CONFIG['sync_interval_days'])),
            'field_mappings': {
                'field1': Variable.get('sync_field1_mapping', default_var=DEFAULT_SYNC_CONFIG['field_mappings']['field1']),
                'field2': Variable.get('sync_field2_mapping', default_var=DEFAULT_SYNC_CONFIG['field_mappings']['field2'])
            }
        }
        
    @staticmethod
    def get_aws_ses_params() -> Dict[str, str]:
        """
        Get AWS SES configuration from Airflow variables with defaults from environment variables
        :return: Dictionary containing AWS SES configuration parameters
        """
        return {
            'region': Variable.get('aws_ses_region', default_var=os.getenv('AWS_SES_REGION', 'us-east-1')),
            'access_key_id': Variable.get('aws_ses_access_key_id', default_var=os.getenv('AWS_SES_ACCESS_KEY_ID', '')),
            'secret_access_key': Variable.get('aws_ses_secret_access_key', default_var=os.getenv('AWS_SES_SECRET_ACCESS_KEY', '')),
            'sender_email': Variable.get('aws_ses_sender_email', default_var=os.getenv('AWS_SES_SENDER_EMAIL', '<EMAIL>'))
        } 